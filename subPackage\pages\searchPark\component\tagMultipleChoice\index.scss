.region-wrap {
  min-height: 520rpx;
  width: 100%;
  color: #20263a;
  font-size: 24rpx;
  font-weight: 400;
}
.region-wrap .title {
  width: 702rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #74798C;
  margin-left: 24rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #EEEEEE;
}
.region-wrap .none {
  background: #F7F7F7;
  color: #20263A;
}
.region-wrap .active {
  color: #FFFFFF;
  background: linear-gradient(#F56E60 0%, #E72410 100%) !important;
}

.region-wrap .list {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: start;
  flex-wrap: wrap;
  padding: 20rpx 0 22rpx 14rpx;
  overflow-y: auto;
}
.region-wrap .customFilterContent {
  width: 100%;
  height: 100%;
}

.region-wrap .list .item {
  width:auto;
  padding: 12rpx 24rpx;
  margin: 10rpx;
  border-radius: 8rpx;
}