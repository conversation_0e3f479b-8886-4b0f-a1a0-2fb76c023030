/* companyPackage/pages/addCard/components/formItem/formItem.scss */
.form-item {
  min-height: 92rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* border-bottom: 1px solid #eee; */
  font-size: 28rpx;
  font-weight: 400;
  color: #20263a;
  position: relative;
  font-family: PingFang SC-Light, PingFang SC;
}

.form-item::after {
  content: " ";
  width: 100%;
  height: 2rpx;
  background: #EEEEEE;
  position: absolute;
  bottom: 0;
  transform: scaleY(0.5);
}

.form-item .label {
  font-weight: 600;
  flex-shrink: 0;
}

.form-item.textarea {
  height: auto;
  display: block;
}

.form-item.textarea .label {
  height: 92rpx;
  line-height: 92rpx;
}

.form-item.textarea textarea {
  width: 100%;
  height: 122rpx;
  margin-bottom: 30rpx;
  position: relative; 
  z-index: 0; 
}
.form-item.textarea .input-box {
  flex-direction: column;
  align-items: flex-end;
  padding-bottom: 26rpx;
}
.form-item.textarea .input-box .count-words {
  font-size: 24rpx;
  color: #9B9EAC;
}
.form-item.textarea .input-box textarea {
  margin-bottom: 10rpx;
}
.input-box {
  display: flex;
  height: 100%;
  align-items: center;
}

.input-box input.text-input {
  text-align: right;
  color: #20263a;
}

.input-box .solt {
  margin-left: 24rpx;
  margin-top: -4rpx;
}

.placeholderClass {
  color: #9B9EAC;
  font-weight: 400;
  font-family: PingFang SC-Light, PingFang SC;
}

.custom_content {
  position: relative;
}

.custom_content::before {
  content: " ";
  height: 2rpx;
  width: 100%;
  background: #EEEEEE;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}

.select-box {
  display: flex;
  height: 100%;
  align-items: center;
}

.select-box .placeholder {
  color: #9B9EAC;
}

.select-box .icon {
  flex-shrink: 0;
  width: 24rpx;
  height: 24rpx;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAALdJREFUSEu11T0OgzAMhuHPmbhYxcpeqRfJykEQgs6sVa7AgZhwlc7F+AeYo/cJkSwTbv5I6g/vkhNjfT0fH+89DoEaB3MPYEugzoscAuNc2p15AaGJIOITXYGIQH33KHIKRBEVEEHUgBcxAR7EDFgRF2BBYgB4AdCAsSX6P+0u4Dcbinj9UzNgiZsBa9wEeOJqwBtXAZH4KRCNi8AVcREYppJB3EtDpNnT8tKfSk5009LX3E5z5gtY2L0ZrugeNQAAAABJRU5ErkJggg==") no-repeat;
  background-size: 24rpx;
}

.select-item-box {
  max-height: 480rpx;
  overflow-y: auto;
  padding: 0 24rpx;
}

.select-box .select-item {
  height: 96rpx;
  color: #74798C;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.select-box .select-item.active .select {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAATlJREFUWEftlj1uwjAYhl8DGwi8komiMLGyU4aeoBfgBqh7pa6MHIKBWyBuwAGQMAONOlSy1KVDEqMgWQpJIP6hSSWS2dHz+P38+TNByR8pmY9KoErg8RLwXDru7vhGdl+hCXz26YzUsBDAwtnxt0iiMAEJP+9cgDV/g1H7+PNdiEASjgCTLuOskARuwf9cIA+eKbDvUfrEOLedESrwlIDn0g8ITEWIZ2fPD6YSqvALAc9tDYH6FiCN6JSaSujAUwl8DTqvocDKVEIXnnkGTCVM4Fe7QFfCFH6zDVUlbOC590CehC08VyBacE3iHnAlgSwJAiwFwbscLPG7XffuUB5GF0lIigCzgSsnIHnJctjCtQVi5ZjDJy9ypOrGHl+vXIL4T2ugMQF8G3ApT7IsYaME7rHzKoF/k8AJqsP2IbiZ1dsAAAAASUVORK5CYII=') no-repeat center center;
  background-size: 100% 100%;
}
.select-box .select-item.active {
  font-weight: 600;
  color: #E72410;
}

.select-box .select-item:not(:first-child)::before {
  content: " ";
  height: 2rpx;
  width: 100%;
  background: #EEEEEE;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}

.inputSelect {
  display: flex;
}

.inputSelect .unit {
  padding-right: 10rpx;
  margin-top: 3rpx;
}

.inputSelect .text-input {
  color: #20263a;
  text-align: right;
  margin-right: 20rpx;
}

.interval-box {
  display: flex;
  align-items: center;
}

.interval-box .interval-input {
  width: 148rpx;
  height: 64rpx;
  text-align: center;
  background-color: #f7f7f7;
  color: #20263a;
}

.list-select {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 24rpx 24rpx;

}

.list-select .select-items {
  width: 31%;
  height: 88rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  font-weight: 400;
  color: #20263A;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16rpx;
  border: none;
}

.list-select .select-items.active {
  background-color: #e7f1fd;
  font-weight: 600;
  color: #E72410;
  border: none;
}

.list-select .select-items:not(:nth-child(3n)) {
  margin-right: 3.5%;
}

/* 模糊地址选择 */
.child-box {
  position: absolute;
  width: fit-content;
  top: 100rpx;
  right: 0;
  background: #fff;
  z-index: 15;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(32, 38, 58, 0.20);
}

.child-box-ul {
  height: 480rpx;
}

.search-li {
  height: 96rpx;
  line-height: 96rpx;
  margin: 0 28rpx;
  border-bottom: 1px solid #f7f7f7;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 670rpx;
}

.search-li .listtext {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}


.search-li .searchHigh {
  color: #20263a !important;
}

.child-box .loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading .size {
  width: 80rpx;
  height: 80rpx;
}

.loading .text {
  padding-top: 20rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #9b9eac;
}

.hover {
  background-color: rgba(116, 121, 140, 0.1) !important;
}