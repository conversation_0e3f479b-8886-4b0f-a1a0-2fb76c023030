/* subPackage/pages/carrierDetail/tap/tap.scss */

/* 组件主体 */
.tap-list-wrapper {
  background: #fff;
  /* max-height: 450rpx;
  overflow: auto; */
}

.tap-list-wrapper .tap-item {
  padding: 28rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 2rpx solid #EEE;
}

.tap-list-wrapper .tap-item:last-child {
  border-bottom: none;
}

.tap-list-wrapper .tap-item image {
  width: 100rpx;
  height: 100rpx;
}

.tap-list-wrapper .tap-item .ent-info {
  width: calc(100% - 124rpx);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.tap-list-wrapper .tap-item .ent-info .title {
  width: 100%;
  display: inline;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
}

.tap-list-wrapper .tap-item .ent-info .show-info-wrapper {
  margin-top: 20rpx;
}

.show-info-wrapper .show-info {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.show-info-wrapper .show-info view {
  width: max-content;
  display: inline-block;
  line-height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.show-info-wrapper .show-info text {
  line-height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
}