/**
 * 数据处理工具类
 * 简化的数据处理逻辑
 */

const {
  BOOLEAN_FIELDS,
  PARENT_TYPE_PREFIXES,
  getComponentConfig,
  filterFields
} = require('./config.js');
const {clone} = require('../../../utils/util.js');

/**
 * 数据处理器类
 */
class DataProcessor {
  /**
   * 处理多选地区数据，过滤父子关系
   * @param {Array} ary - 原始数组
   * @returns {Array} 处理后的数组
   */
  static handleMultiple(ary = []) {
    if (!ary.length) return [];

    let tempArr = JSON.parse(JSON.stringify(ary));
    let checkedArr = tempArr.filter(
      i => i.status === 'checked' && !i.ischildren
    );
    checkedArr = checkedArr.filter(
      item => !checkedArr.map(item => item.code).includes(item.parent)
    );

    if (!checkedArr.length) return ary;

    checkedArr.forEach(i => {
      tempArr = tempArr.filter(item => i.code !== item.parent);
    });

    return tempArr;
  }

  /**
   * 处理搜索数据，转换为API所需格式
   * @param {Object} params - 搜索参数
   * @returns {string} JSON字符串格式的处理结果
   */
  static handleData(params) {
    const obj = clone(params);

    // 处理弹窗选择相关数据
    obj['areas'] =
      params.regionData?.length > 0
        ? this.handleMultiple(params.regionData)
            .filter(i => i.status === 'checked')
            .map(i => i.code)
        : [];

    obj['trade_types'] =
      params.eleseic_data?.length > 0
        ? this.handleMultiple(params.eleseic_data)
            .filter(i => i.status === 'checked')
            .map(i => i.code)
        : [];

    obj['ent_type'] =
      params.enttype_data?.length > 0
        ? this.handleMultiple(params.enttype_data)
            .filter(i => i.status === 'checked')
            .map(i => i.code)
        : [];

    obj['ent_cert'] =
      params.all_cert_data?.length > 0
        ? this.handleMultiple(params.all_cert_data)
            .filter(i => i.status === 'checked')
            .map(i => i.code)
        : [];

    // 产业链特殊处理
    obj['chain_codes'] =
      params.chain_codes_data?.length > 0
        ? params.chain_codes_data
            .filter(i => i.active === true)
            .map(i => i.code)
        : [];

    return JSON.stringify(obj);
  }

  /**
   * 处理数据结构，转换布尔值和父子关系
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  static handleStructure(data) {
    const params = Object.assign({}, data);

    // 处理布尔类型字段
    BOOLEAN_FIELDS.forEach(item => {
      if (params[item]?.length) {
        params[item] = JSON.parse(params[item][0]); // 将 "true" => true
      } else {
        delete params[item];
      }
    });

    // 处理父子关系字段
    PARENT_TYPE_PREFIXES.forEach(prefix => {
      Object.keys(params).forEach(key => {
        if (key.startsWith(prefix)) {
          const suffix = key.slice(prefix.length);
          const parentKey = key.slice(0, prefix.length - 1);
          const obj = {};

          obj[suffix] = params[key];
          params[parentKey] = {
            ...params[parentKey],
            ...obj
          };
          delete params[key];
        }
      });
    });

    return params;
  }

  /**
   * 获取弹窗数据的显示名称
   * @param {Array} data - 弹窗数据
   * @returns {string} 显示名称
   */
  static getNameFromPop(data) {
    // 产业链单独处理
    if (data?.length > 0 && data?.[0]?.MultipleCelectionSingleSelection) {
      return data[0].name;
    }

    const arr = [];

    data?.length > 0 &&
      data.forEach((item, idx, oldArr) => {
        if (item.status === 'checked') {
          if (item.parent && item.parent !== '') {
            // 有父级
            const parent = oldArr.filter(
              parentItem => item.parent === parentItem.code
            )[0];
            parent.status === 'checked' ? arr.push(parent) : arr.push(item);
          } else {
            // 没有父级
            arr.push(item);
          }
        }
      });

    // 去重
    const newObj = {};
    const uniqueArr = arr.reduce((preVal, curVal) => {
      newObj[curVal.code] ? '' : (newObj[curVal.code] = preVal.push(curVal));
      return preVal;
    }, []);

    return uniqueArr.map(item => item.name).join('');
  }

  /**
   * 获取高亮状态
   * @param {Object} paramsData - 参数数据
   * @returns {boolean} 是否有高亮
   */
  static getHeightStatus(paramsData) {
    let isHeight = false;

    Object.keys(paramsData).some(key => {
      // 处理自定义输入类型
      if (this.isCustomInputField(key)) {
        if (
          paramsData[key].length > 0 &&
          (paramsData[key][0]?.start || paramsData[key][0]?.end)
        ) {
          isHeight = true;
          return true;
        }
      }
      // 处理输入框类型
      else if (this.isInputField(key)) {
        if (paramsData[key].trim().length > 0) {
          isHeight = true;
          return true;
        }
      }
      // 处理其他类型
      else if (paramsData[key].length > 0) {
        isHeight = true;
        return true;
      }

      return false;
    });

    return isHeight;
  }

  /**
   * 判断是否为自定义输入字段
   * @param {string} key - 字段名
   * @returns {boolean} 判断结果
   */
  static isCustomInputField(key) {
    const customInputFields = [
      'register_time',
      'register_capital',
      'super_dimension_social_num'
    ];
    return customInputFields.includes(key);
  }

  /**
   * 判断是否为输入框字段
   * @param {string} key - 字段名
   * @returns {boolean} 判断结果
   */
  static isInputField(key) {
    const inputFields = ['ent_name'];
    return inputFields.includes(key);
  }

  /**
   * 根据组件类型过滤数据
   * @param {Array} renderList - 原始渲染列表
   * @param {string} componentType - 组件类型 ('hunt' | 'huntCopy')
   * @returns {Array} 过滤后的列表
   */
  static filterDataByComponent(renderList, componentType) {
    const config = getComponentConfig(componentType);
    let filteredList = JSON.parse(JSON.stringify(renderList));

    // 过滤排除的字段
    if (config.excludeFields.length > 0) {
      filteredList = filteredList.filter(item => {
        return !config.excludeFields.includes(item.type);
      });
    }

    // 过滤分类中排除的字段
    if (Object.keys(config.excludeFromCategories).length > 0) {
      filteredList = filteredList.filter(item => {
        const categoryExcludes = config.excludeFromCategories[item.title];
        if (categoryExcludes && item.map) {
          item.map = item.map.filter(
            mapItem => !categoryExcludes.includes(mapItem.key)
          );
        }
        return item;
      });
    }

    return filteredList;
  }

  /**
   * 重置搜索参数
   * @param {Object} defaultParams - 默认参数
   * @returns {Object} 重置后的参数
   */
  static resetParams(defaultParams) {
    return JSON.parse(JSON.stringify(defaultParams));
  }

  /**
   * 重置额外状态
   * @returns {Object} 重置后的状态对象
   */
  static resetExtraStates() {
    return {
      minCapital: '',
      maxCapital: '',
      minDate: '',
      maxDate: '',
      date: '',
      capitalActive: false,
      socialActive: false,
      socialminPeson: '',
      socialmaxPeson: '',
      dateActive: false,
      dateType: ''
    };
  }

  /**
   * 验证搜索参数
   * @param {Object} paramsData - 参数数据
   * @returns {boolean} 验证结果
   */
  static validateSearchParams(paramsData = {}) {
    const customInputFields = [
      'register_time',
      'register_capital',
      'super_dimension_social_num'
    ];

    const hasError = Object.keys(paramsData).some(key => {
      if (customInputFields.includes(key) && paramsData[key].length === 1) {
        const item = paramsData[key][0];

        if (key === 'register_time') {
          const startTime = new Date(item.start).getTime();
          const endTime = new Date(item.end).getTime();

          if (startTime >= endTime) {
            wx.showToast({
              title: '最低年限不能大于等于最高年限',
              icon: 'none'
            });
            return true;
          }
        } else if (
          ['register_capital', 'super_dimension_social_num'].includes(key)
        ) {
          if (parseFloat(item.start) >= parseFloat(item.end)) {
            const tipMap = {
              register_capital: {min: '最低资本', max: '最高资本'},
              super_dimension_social_num: {min: '最低人数', max: '最高人数'}
            };
            const tip = tipMap[key];

            wx.showToast({
              title: `${tip.min}不能大于等于${tip.max}`,
              icon: 'none'
            });
            return true;
          }
        }
      }
      return false;
    });

    return !hasError;
  }
}

module.exports = DataProcessor;
