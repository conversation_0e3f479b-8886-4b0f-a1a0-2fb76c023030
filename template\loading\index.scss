
/* 加载中 */
.spinner {
    margin: 45% auto;
    width: 100rpx;
    height: 50rpx;
    text-align: center;
    font-size: 10rpx;
  }
  
  .spinner .kuai {
    background-color: rgba(116, 121, 140,0.6);
    height: 100%;
    width: 5rpx;
    display: inline-block;
    -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
    animation: stretchdelay 1.2s infinite ease-in-out;
    margin-left: 8rpx;
  }
  
  .spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s;
  }
  
  .spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s;
  }
  
  .spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }
  
  .spinner .rect5 {
    -webkit-animation-delay: -0.8s;
    animation-delay: -0.8s;
  }
  
  @-webkit-keyframes stretchdelay {
  
    0%,
    40%,
    100% {
      -webkit-transform: scaleY(0.4)
    }
  
    20% {
      -webkit-transform: scaleY(1.0)
    }
  }
  
  @keyframes stretchdelay {
  
    0%,
    40%,
    100% {
      transform: scaleY(0.4);
      -webkit-transform: scaleY(0.4);
    }
  
    20% {
      transform: scaleY(1.0);
      -webkit-transform: scaleY(1.0);
    }
  }