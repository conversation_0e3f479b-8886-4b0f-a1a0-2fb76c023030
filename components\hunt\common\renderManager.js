/**
 * 渲染管理器
 * 优化数据结构和渲染方式，提升性能和可维护性
 */

const { COMPONENT_CONFIGS } = require('./constants.js');

/**
 * 虚拟列表项类
 * 优化大数据量渲染性能
 */
class VirtualListItem {
  constructor(data, index) {
    this.id = data.id || `item_${index}`;
    this.type = data.type;
    this.title = data.title;
    this.visible = true;
    this.height = data.height || 80; // 预估高度
    this.data = data;
    this._dirty = false; // 脏标记，用于优化更新
  }

  /**
   * 标记为脏数据
   */
  markDirty() {
    this._dirty = true;
  }

  /**
   * 清除脏标记
   */
  clearDirty() {
    this._dirty = false;
  }

  /**
   * 是否需要更新
   */
  isDirty() {
    return this._dirty;
  }

  /**
   * 更新数据
   */
  updateData(newData) {
    if (JSON.stringify(this.data) !== JSON.stringify(newData)) {
      this.data = { ...this.data, ...newData };
      this.markDirty();
    }
  }
}

/**
 * 数据结构优化器
 * 将平铺的数据结构转换为树形结构，提升查找和更新效率
 */
class DataStructureOptimizer {
  /**
   * 构建索引映射
   * @param {Array} list - 原始列表
   * @returns {Object} 索引映射
   */
  static buildIndexMap(list) {
    const indexMap = {
      byType: new Map(),
      byTitle: new Map(),
      byCategory: new Map()
    };

    list.forEach((item, index) => {
      // 按类型索引
      if (!indexMap.byType.has(item.type)) {
        indexMap.byType.set(item.type, []);
      }
      indexMap.byType.get(item.type).push(index);

      // 按标题索引
      indexMap.byTitle.set(item.title, index);

      // 按分类索引
      if (item.category) {
        if (!indexMap.byCategory.has(item.category)) {
          indexMap.byCategory.set(item.category, []);
        }
        indexMap.byCategory.get(item.category).push(index);
      }
    });

    return indexMap;
  }

  /**
   * 构建树形结构
   * @param {Array} list - 原始列表
   * @returns {Object} 树形结构
   */
  static buildTreeStructure(list) {
    const tree = {
      categories: new Map(),
      items: new Map()
    };

    list.forEach((item, index) => {
      const category = item.category || '默认分类';
      
      if (!tree.categories.has(category)) {
        tree.categories.set(category, {
          name: category,
          items: [],
          expanded: false,
          visible: true
        });
      }

      const treeItem = {
        ...item,
        index,
        parent: category,
        children: item.list || []
      };

      tree.categories.get(category).items.push(treeItem);
      tree.items.set(item.type, treeItem);
    });

    return tree;
  }

  /**
   * 扁平化树形结构
   * @param {Object} tree - 树形结构
   * @returns {Array} 扁平化列表
   */
  static flattenTree(tree) {
    const result = [];
    
    tree.categories.forEach((category) => {
      if (category.visible) {
        category.items.forEach(item => {
          if (item.visible !== false) {
            result.push(item);
          }
        });
      }
    });

    return result;
  }
}

/**
 * 渲染性能优化器
 */
class RenderPerformanceOptimizer {
  constructor() {
    this.updateQueue = [];
    this.isUpdating = false;
    this.frameId = null;
  }

  /**
   * 批量更新
   * @param {Function} updateFn - 更新函数
   */
  batchUpdate(updateFn) {
    this.updateQueue.push(updateFn);
    
    if (!this.isUpdating) {
      this.scheduleUpdate();
    }
  }

  /**
   * 调度更新
   */
  scheduleUpdate() {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
    }

    this.frameId = requestAnimationFrame(() => {
      this.flushUpdates();
    });
  }

  /**
   * 执行更新队列
   */
  flushUpdates() {
    this.isUpdating = true;
    
    while (this.updateQueue.length > 0) {
      const updateFn = this.updateQueue.shift();
      try {
        updateFn();
      } catch (error) {
        console.error('Update error:', error);
      }
    }
    
    this.isUpdating = false;
    this.frameId = null;
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
    }
    this.updateQueue = [];
    this.isUpdating = false;
  }
}

/**
 * 渲染管理器主类
 */
class RenderManager {
  constructor(componentType = 'hunt') {
    this.componentType = componentType;
    this.config = COMPONENT_CONFIGS[componentType] || COMPONENT_CONFIGS.hunt;
    this.optimizer = new RenderPerformanceOptimizer();
    this.indexMap = null;
    this.treeStructure = null;
    this.virtualItems = [];
    this.visibleRange = { start: 0, end: 50 }; // 虚拟滚动可见范围
  }

  /**
   * 初始化数据
   * @param {Array} rawData - 原始数据
   */
  initialize(rawData) {
    // 过滤数据
    const filteredData = this.filterData(rawData);
    
    // 构建索引
    this.indexMap = DataStructureOptimizer.buildIndexMap(filteredData);
    
    // 构建树形结构
    this.treeStructure = DataStructureOptimizer.buildTreeStructure(filteredData);
    
    // 创建虚拟列表项
    this.virtualItems = filteredData.map((item, index) => 
      new VirtualListItem(item, index)
    );

    return this.getVisibleData();
  }

  /**
   * 过滤数据
   * @param {Array} data - 原始数据
   * @returns {Array} 过滤后的数据
   */
  filterData(data) {
    let filtered = [...data];

    // 过滤排除的字段
    if (this.config.excludeFields.length > 0) {
      filtered = filtered.filter(item => 
        !this.config.excludeFields.includes(item.type)
      );
    }

    // 过滤分类中排除的字段
    if (Object.keys(this.config.excludeFromCategories).length > 0) {
      filtered = filtered.map(item => {
        const categoryExcludes = this.config.excludeFromCategories[item.title];
        if (categoryExcludes && item.map) {
          item.map = item.map.filter(mapItem => 
            !categoryExcludes.includes(mapItem.key)
          );
        }
        return item;
      });
    }

    return filtered;
  }

  /**
   * 获取可见数据
   * @returns {Array} 可见数据
   */
  getVisibleData() {
    const { start, end } = this.visibleRange;
    return this.virtualItems
      .slice(start, end)
      .filter(item => item.visible)
      .map(item => item.data);
  }

  /**
   * 更新可见范围
   * @param {number} start - 开始索引
   * @param {number} end - 结束索引
   */
  updateVisibleRange(start, end) {
    this.visibleRange = { start, end };
    return this.getVisibleData();
  }

  /**
   * 更新单个项目
   * @param {string} type - 项目类型
   * @param {Object} updates - 更新数据
   */
  updateItem(type, updates) {
    const item = this.virtualItems.find(item => item.type === type);
    if (item) {
      this.optimizer.batchUpdate(() => {
        item.updateData(updates);
      });
    }
  }

  /**
   * 批量更新项目
   * @param {Array} updates - 更新列表
   */
  batchUpdateItems(updates) {
    this.optimizer.batchUpdate(() => {
      updates.forEach(({ type, data }) => {
        const item = this.virtualItems.find(item => item.type === type);
        if (item) {
          item.updateData(data);
        }
      });
    });
  }

  /**
   * 搜索项目
   * @param {string} query - 搜索关键词
   * @returns {Array} 搜索结果
   */
  search(query) {
    if (!query) return this.getVisibleData();

    const results = this.virtualItems.filter(item => {
      const data = item.data;
      return data.title?.includes(query) || 
             data.type?.includes(query) ||
             data.content?.includes(query);
    });

    return results.map(item => item.data);
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      total: this.virtualItems.length,
      visible: this.virtualItems.filter(item => item.visible).length,
      dirty: this.virtualItems.filter(item => item.isDirty()).length,
      categories: this.treeStructure.categories.size
    };
  }

  /**
   * 清理资源
   */
  destroy() {
    this.optimizer.destroy();
    this.virtualItems = [];
    this.indexMap = null;
    this.treeStructure = null;
  }
}

module.exports = {
  RenderManager,
  VirtualListItem,
  DataStructureOptimizer,
  RenderPerformanceOptimizer
};
