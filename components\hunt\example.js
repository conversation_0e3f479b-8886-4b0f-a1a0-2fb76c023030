/**
 * Hunt 搜索组件使用示例
 * 展示如何在页面中使用优化后的搜索组件
 */

// 引入工具函数
const { 
  clearChildComponent, 
  fillChildComponent, 
  checkoutSear,
  SearchParamsValidator,
  SearchResultProcessor 
} = require('./common/utils.js');

const { PerformanceMonitor } = require('./common/performanceMonitor.js');

/**
 * 页面使用示例
 */
Page({
  data: {
    searchResults: [],
    loading: false,
    performanceStats: {}
  },

  onLoad() {
    // 初始化性能监控
    this.performanceMonitor = new PerformanceMonitor();
    this.performanceMonitor.enable();

    // 获取清空和回填函数
    this.clearSearch = clearChildComponent(this, '#hunt');
    this.fillSearch = fillChildComponent(this, '#hunt');
  },

  onUnload() {
    // 清理资源
    if (this.performanceMonitor) {
      this.performanceMonitor.destroy();
    }
  },

  /**
   * 处理搜索提交
   * @param {Object} e - 事件对象
   */
  onSearchSubmit(e) {
    const { isHeight, paramsData } = e.detail;
    
    // 记录搜索操作
    this.performanceMonitor.recordSearchOperation();
    
    // 验证搜索参数
    const validator = new SearchParamsValidator();
    const validationResult = validator.validate(paramsData, ['ent_name']);
    
    if (!validationResult.isValid) {
      wx.showToast({
        title: validationResult.message,
        icon: 'none'
      });
      return;
    }

    // 执行搜索
    this.performSearch(paramsData);
  },

  /**
   * 执行搜索
   * @param {Object} params - 搜索参数
   */
  async performSearch(params) {
    this.setData({ loading: true });
    
    try {
      // 使用性能监控测量搜索时间
      const results = await this.performanceMonitor.measureAsync(async () => {
        // 模拟API调用
        return await this.mockSearchAPI(params);
      }, 'search');

      // 处理搜索结果
      const processor = new SearchResultProcessor();
      const formattedResults = processor.formatResult(results);
      const summary = processor.generateSummary(params);

      this.setData({
        searchResults: formattedResults,
        loading: false
      });

      // 显示搜索摘要
      console.log('搜索摘要:', summary);

    } catch (error) {
      console.error('搜索失败:', error);
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 模拟搜索API
   * @param {Object} params - 搜索参数
   * @returns {Promise<Array>} 搜索结果
   */
  mockSearchAPI(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟返回搜索结果
        const mockResults = [
          { id: 1, name: '企业A', type: '科技公司' },
          { id: 2, name: '企业B', type: '制造业' },
          { id: 3, name: '企业C', type: '服务业' }
        ];
        resolve(mockResults);
      }, 1000);
    });
  },

  /**
   * 清空搜索条件
   */
  onClearSearch() {
    this.clearSearch();
    this.setData({
      searchResults: [],
      loading: false
    });
  },

  /**
   * 回填搜索条件
   */
  onFillSearch() {
    const sampleData = {
      ent_name: '示例企业',
      regionData: [],
      eleseic_data: []
    };
    
    this.fillSearch(sampleData);
  },

  /**
   * 显示性能统计
   */
  onShowPerformanceStats() {
    const report = this.performanceMonitor.generateReport();
    const suggestions = this.performanceMonitor.getOptimizationSuggestions();
    
    this.setData({ performanceStats: report });
    
    console.log('性能报告:', report);
    console.log('优化建议:', suggestions);
    
    wx.showModal({
      title: '性能统计',
      content: `
        平均渲染时间: ${report.averageRenderTime.toFixed(2)}ms
        平均更新时间: ${report.averageUpdateTime.toFixed(2)}ms
        性能评分: ${report.performanceScore}
        优化建议: ${suggestions.join('; ')}
      `,
      showCancel: false
    });
  },

  /**
   * 处理VIP弹窗
   * @param {Object} e - 事件对象
   */
  onVipShow(e) {
    console.log('需要显示VIP弹窗');
    // 这里处理VIP弹窗逻辑
    wx.showModal({
      title: '升级VIP',
      content: '该功能需要VIP权限，是否立即升级？',
      confirmText: '立即升级',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 跳转到VIP升级页面
          wx.navigateTo({
            url: '/pages/vip/vip'
          });
        }
      }
    });
  },

  /**
   * 批量操作示例
   */
  onBatchOperation() {
    // 批量验证多个搜索条件
    const batchParams = [
      { ent_name: '企业1', areas: ['北京'] },
      { ent_name: '企业2', areas: ['上海'] },
      { ent_name: '企业3', areas: ['深圳'] }
    ];

    const validator = new SearchParamsValidator();
    const results = batchParams.map(params => {
      return {
        params,
        validation: validator.validate(params, ['ent_name'])
      };
    });

    const validParams = results
      .filter(result => result.validation.isValid)
      .map(result => result.params);

    console.log('有效的搜索参数:', validParams);

    if (validParams.length > 0) {
      // 执行批量搜索
      this.performBatchSearch(validParams);
    }
  },

  /**
   * 批量搜索
   * @param {Array} paramsList - 参数列表
   */
  async performBatchSearch(paramsList) {
    this.setData({ loading: true });

    try {
      const promises = paramsList.map(params => this.mockSearchAPI(params));
      const results = await Promise.all(promises);
      
      // 合并结果
      const mergedResults = results.flat();
      
      this.setData({
        searchResults: mergedResults,
        loading: false
      });

    } catch (error) {
      console.error('批量搜索失败:', error);
      this.setData({ loading: false });
    }
  },

  /**
   * 导出搜索结果
   */
  onExportResults() {
    const { searchResults } = this.data;
    
    if (searchResults.length === 0) {
      wx.showToast({
        title: '暂无搜索结果',
        icon: 'none'
      });
      return;
    }

    // 格式化导出数据
    const exportData = searchResults.map(item => ({
      企业名称: item.name,
      企业类型: item.type,
      导出时间: new Date().toLocaleString()
    }));

    console.log('导出数据:', exportData);
    
    wx.showToast({
      title: '导出成功',
      icon: 'success'
    });
  }
});

/**
 * 组件使用示例（在其他页面中引用）
 */
const ComponentUsageExample = {
  /**
   * 在页面中使用hunt组件
   */
  useInPage() {
    return `
      <!-- 在wxml中使用 -->
      <hunt 
        id="hunt"
        bind:submit="onSearchSubmit"
        bind:vip="onVipShow"
        wrapHeight="calc(100vh - 200rpx)"
        isPage="{{true}}"
      />
      
      <!-- 搜索结果展示 -->
      <view wx:if="{{searchResults.length > 0}}">
        <view wx:for="{{searchResults}}" wx:key="id">
          {{item.name}} - {{item.type}}
        </view>
      </view>
    `;
  },

  /**
   * 在组件中使用hunt组件
   */
  useInComponent() {
    return `
      Component({
        data: {
          searchData: {}
        },
        
        methods: {
          onSearchChange(e) {
            const { paramsData } = e.detail;
            this.setData({ searchData: paramsData });
            
            // 触发父组件事件
            this.triggerEvent('searchchange', { data: paramsData });
          }
        }
      });
    `;
  }
};

module.exports = {
  ComponentUsageExample
};
