/**
 * Hunt 搜索组件使用示例
 * 展示如何在页面中使用简化后的搜索组件
 */

const { 
  clearChildComponent, 
  fillChildComponent, 
  validateSearchParams,
  generateSearchSummary,
  addSearchField,
  removeSearchField
} = require('./common/utils.js');

/**
 * 页面使用示例
 */
Page({
  data: {
    searchResults: [],
    loading: false
  },

  onLoad() {
    // 获取清空和回填函数
    this.clearSearch = clearChildComponent(this, '#hunt');
    this.fillSearch = fillChildComponent(this, '#hunt');
    
    // 动态添加自定义字段（可选）
    this.addCustomFields();
  },

  /**
   * 添加自定义字段示例
   */
  addCustomFields() {
    // 添加税号输入框
    addSearchField('input', 'tax_number', {
      label: '税号',
      placeholder: '请输入税号',
      maxLength: 18
    });

    // 添加企业等级单选
    addSearchField('radio', 'company_level', {
      label: '企业等级'
    });

    // 添加业务范围多选
    addSearchField('multiSelect', 'business_scope', {
      label: '业务范围'
    });
  },

  /**
   * 处理搜索提交
   * @param {Object} e - 事件对象
   */
  onSearchSubmit(e) {
    const { isHeight, paramsData } = e.detail;
    
    // 验证搜索参数
    const validation = validateSearchParams(paramsData, ['ent_name']);
    
    if (!validation.isValid) {
      wx.showToast({
        title: validation.message,
        icon: 'none'
      });
      return;
    }

    // 生成搜索摘要
    const summary = generateSearchSummary(paramsData);
    console.log('搜索摘要:', summary);

    // 执行搜索
    this.performSearch(paramsData);
  },

  /**
   * 执行搜索
   * @param {Object} params - 搜索参数
   */
  async performSearch(params) {
    this.setData({ loading: true });
    
    try {
      // 模拟API调用
      const results = await this.mockSearchAPI(params);

      this.setData({
        searchResults: results,
        loading: false
      });

      wx.showToast({
        title: `找到 ${results.length} 条结果`,
        icon: 'success'
      });

    } catch (error) {
      console.error('搜索失败:', error);
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 模拟搜索API
   * @param {Object} params - 搜索参数
   * @returns {Promise<Array>} 搜索结果
   */
  mockSearchAPI(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟返回搜索结果
        const mockResults = [
          { id: 1, name: '企业A', type: '科技公司', region: '北京' },
          { id: 2, name: '企业B', type: '制造业', region: '上海' },
          { id: 3, name: '企业C', type: '服务业', region: '深圳' }
        ];
        resolve(mockResults);
      }, 1000);
    });
  },

  /**
   * 清空搜索条件
   */
  onClearSearch() {
    this.clearSearch();
    this.setData({
      searchResults: [],
      loading: false
    });
  },

  /**
   * 回填搜索条件示例
   */
  onFillSearchExample() {
    const sampleData = {
      ent_name: '示例企业',
      regionData: [],
      eleseic_data: [],
      technology_types: ['高新技术企业']
    };
    
    this.fillSearch(sampleData);
  },

  /**
   * 处理VIP弹窗
   * @param {Object} e - 事件对象
   */
  onVipShow(e) {
    console.log('需要显示VIP弹窗');
    wx.showModal({
      title: '升级VIP',
      content: '该功能需要VIP权限，是否立即升级？',
      confirmText: '立即升级',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 跳转到VIP升级页面
          wx.navigateTo({
            url: '/pages/vip/vip'
          });
        }
      }
    });
  },

  /**
   * 导出搜索结果
   */
  onExportResults() {
    const { searchResults } = this.data;
    
    if (searchResults.length === 0) {
      wx.showToast({
        title: '暂无搜索结果',
        icon: 'none'
      });
      return;
    }

    // 格式化导出数据
    const exportData = searchResults.map(item => ({
      企业名称: item.name,
      企业类型: item.type,
      所在地区: item.region,
      导出时间: new Date().toLocaleString()
    }));

    console.log('导出数据:', exportData);
    
    wx.showToast({
      title: '导出成功',
      icon: 'success'
    });
  },

  /**
   * 动态移除字段示例
   */
  onRemoveCustomField() {
    // 移除之前添加的税号字段
    removeSearchField('tax_number');
    
    wx.showToast({
      title: '已移除税号字段',
      icon: 'success'
    });
  }
});

/**
 * 组件使用示例（在wxml中）
 */
const WXMLExample = `
<!-- 在页面wxml中使用 -->
<view class="search-container">
  <hunt 
    id="hunt"
    bind:submit="onSearchSubmit"
    bind:vip="onVipShow"
    wrapHeight="calc(100vh - 200rpx)"
    isPage="{{true}}"
  />
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button bind:tap="onClearSearch">清空条件</button>
    <button bind:tap="onFillSearchExample">示例回填</button>
    <button bind:tap="onExportResults">导出结果</button>
    <button bind:tap="onRemoveCustomField">移除自定义字段</button>
  </view>
  
  <!-- 搜索结果展示 -->
  <view class="search-results" wx:if="{{searchResults.length > 0}}">
    <view class="result-item" wx:for="{{searchResults}}" wx:key="id">
      <text class="company-name">{{item.name}}</text>
      <text class="company-type">{{item.type}}</text>
      <text class="company-region">{{item.region}}</text>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>搜索中...</text>
  </view>
</view>
`;

/**
 * 样式示例（在wxss中）
 */
const WXSSExample = `
.search-container {
  padding: 20rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  margin: 20rpx 0;
}

.action-buttons button {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

.search-results {
  margin-top: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.company-name {
  font-weight: bold;
  color: #333;
}

.company-type {
  color: #666;
}

.company-region {
  color: #999;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #666;
}
`;

module.exports = {
  WXMLExample,
  WXSSExample
};
