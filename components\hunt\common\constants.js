/**
 * 搜索组件常量配置
 * 统一管理所有搜索相关的常量定义
 */

// 搜索参数初始值
const DEFAULT_PARAMS = {
  ent_name: '', // 企业名称
  regionData: [], // 地区
  eleseic_data: [], // 行业
  technology_types: [], // 科技型企业
  listed_status: [], // 上市状态
  super_dimension_patent_category: [], // 专利内容
  expand_status: [], // 疑似扩张
  ent_scale: [], // 企业规模
  benefit_assess: [], // 效益评估
  register_time: [], // 注册时间
  register_capital: [], // 注册资本
  ent_status: [], // 企业状态
  shit_type: [], // 实体类型
  enttype_data: [], // 企业类型
  all_cert_data: [], // 企业许可
  contact_style: [], // 联系方式
  tel_data: [], // 联系手机号码
  email_data: [], // 联系邮箱
  super_dimension_social_num: [], // 参保人数
  super_dimension_biding: [], // 招投标
  super_dimension_job_info: [], // 招聘
  tax_credit: [], // 纳税信用
  financing_info: [], // 融资信息
  super_dimension_trademark: [], // 商标信息
  super_dimension_patent: [], // 专利信息
  super_dimension_android_app: [], // 安卓APP
  super_dimension_ios_app: [], // 苹果APP
  super_dimension_mini_app: [], // 小程序
  super_dimension_wx_extension: [], // 微信公众号
  super_dimension_weibo_extension: [], // 微博
  untrustworthy_info_data: [], // 失信信息
  judgment_doc_data: [], // 裁判文书
  adminstrative_penalties_data: [], // 行政处罚
  chattel_mortage_data: [], // 动产抵押
  abnormal_operation_data: [], // 经营异常
  software_copyright_data: [], // 软件著作权
  work_copyright_data: [], // 作品著作权
  super_dimension_website: [], // 官网信息
  super_dimension_icp: [], // ICP备案
  chain_codes_data: [], // 所属产业链
  leading_ent: [] // 龙头企业
};

// 搜索类型枚举
const SEARCH_TYPES = {
  RADIO: 'radio', // 单选
  MULTI_SELECT: 'multiSelect', // 多选
  MULTI_INPUT: 'multiInput', // 多选+自定义输入
  POP: 'pop', // 弹窗选择
  INPUT: 'input', // 输入框
  DATE_POP: 'datePop', // 日期弹窗
  RANGE_INPUT: 'rangeInput' // 范围输入
};

// 单选字段配置
const RADIO_FIELDS = {
  list: [
    'tel_data',
    'contact_style',
    'email_data',
    'super_dimension_biding',
    'tax_credit',
    'super_dimension_trademark',
    'super_dimension_android_app',
    'super_dimension_ios_app',
    'super_dimension_mini_app',
    'super_dimension_wx_extension',
    'untrustworthy_info_data',
    'judgment_doc_data',
    'adminstrative_penalties_data',
    'chattel_mortage_data',
    'abnormal_operation_data',
    'software_copyright_data',
    'work_copyright_data',
    'super_dimension_website',
    'super_dimension_icp',
    'super_dimension_weibo_extension',
    'super_dimension_patent',
    'super_dimension_job_info',
    'leading_ent'
  ],
  map: {
    tel_data: '联系方式',
    contact_style: '手机号码',
    email_data: '联系邮箱',
    super_dimension_biding: '招投标',
    super_dimension_job_info: '招聘',
    tax_credit: '纳税信用',
    super_dimension_trademark: '商标信息',
    super_dimension_patent: '专利信息',
    super_dimension_android_app: '安卓APP',
    super_dimension_ios_app: '苹果APP',
    super_dimension_mini_app: '小程序',
    super_dimension_wx_extension: '微信公众号',
    super_dimension_weibo_extension: '微博',
    untrustworthy_info_data: '失信信息',
    judgment_doc_data: '裁判文书',
    adminstrative_penalties_data: '行政处罚',
    chattel_mortage_data: '动产抵押',
    abnormal_operation_data: '经营异常',
    software_copyright_data: '软件著作权',
    work_copyright_data: '作品著作权',
    super_dimension_website: '官网信息',
    super_dimension_icp: 'ICP备案',
    leading_ent: '龙头企业'
  }
};

// 多选字段配置
const MULTI_SELECT_FIELDS = {
  list: [
    'capital_event',
    'shit_type',
    'ent_status',
    'technology_types',
    'financing_info',
    'listed_status',
    'super_dimension_patent_category',
    'expand_status'
  ],
  map: {
    capital_event: '资本事件',
    shit_type: '实体类型',
    ent_status: '企业状态',
    technology_types: '科技型企业',
    financing_info: '融资信息',
    listed_status: '上市状态',
    super_dimension_patent_category: '专利内容',
    expand_status: '疑似扩张'
  }
};

// 范围输入字段配置
const RANGE_INPUT_FIELDS = {
  list: ['register_capital', 'super_dimension_social_num'],
  map: {
    register_capital: '注册资本',
    super_dimension_social_num: '参保人数'
  },
  config: {
    register_capital: {
      max: '最高资本',
      min: '最低资本',
      unit: '万元',
      type: 'number',
      maxLength: 10
    },
    super_dimension_social_num: {
      max: '最高人数',
      min: '最低人数',
      unit: '人',
      type: 'number',
      maxLength: 5
    }
  }
};

// 日期弹窗字段配置
const DATE_POP_FIELDS = {
  list: ['register_time'],
  map: {
    register_time: '注册时间'
  },
  config: {
    register_time: {
      max: '最高年限',
      min: '最低年限',
      unit: '年'
    }
  }
};

// 弹窗选择字段配置
const POP_FIELDS = {
  areas: {
    data: 'regionData',
    label: '所在地区'
  },
  trade_types: {
    data: 'eleseic_data',
    label: '所属行业'
  },
  ent_type: {
    data: 'enttype_data',
    label: '企业类型'
  },
  ent_cert: {
    data: 'all_cert_data',
    label: '企业许可'
  },
  chain_codes: {
    data: 'chain_codes_data',
    label: '所属产业链'
  }
};

// 输入框字段配置
const INPUT_FIELDS = {
  list: ['ent_name'],
  map: {
    ent_name: '企业名称'
  }
};

// 布尔类型字段
const BOOLEAN_FIELDS = [
  'super_dimension_biding',
  'super_dimension_job_info',
  'super_dimension_trademark',
  'super_dimension_patent',
  'super_dimension_android_app',
  'super_dimension_ios_app',
  'super_dimension_mini_app',
  'super_dimension_wx_extension',
  'super_dimension_weibo_extension',
  'super_dimension_website',
  'super_dimension_icp',
  'leading_ent'
];

// 父级类型前缀
const PARENT_TYPE_PREFIXES = ['super_dimension_'];

// 组件配置差异
const COMPONENT_CONFIGS = {
  hunt: {
    excludeFields: [],
    excludeFromCategories: {}
  },
  huntCopy: {
    excludeFields: ['chain_codes'],
    excludeFromCategories: {
      产业优选: ['chain_codes']
    }
  }
};

module.exports = {
  DEFAULT_PARAMS,
  SEARCH_TYPES,
  RADIO_FIELDS,
  MULTI_SELECT_FIELDS,
  RANGE_INPUT_FIELDS,
  DATE_POP_FIELDS,
  POP_FIELDS,
  INPUT_FIELDS,
  BOOLEAN_FIELDS,
  PARENT_TYPE_PREFIXES,
  COMPONENT_CONFIGS
};