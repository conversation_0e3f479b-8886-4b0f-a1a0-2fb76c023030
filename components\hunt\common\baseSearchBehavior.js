/**
 * 基础搜索行为
 * 提供搜索组件的通用行为和方法
 */

const {DEFAULT_PARAMS} = require('./constants.js');
const DataProcessor = require('./dataProcessor.js');
const {SearchStrategyFactory} = require('./searchStrategies.js');
const {clone} = require('../../../utils/util.js');
const {hijack} = require('../../../utils/route.js');
const {debounce, handleSearchHight} = require('../../../utils/formate.js');
const {common} = require('../../../service/api.js');
const {hasPrivile} = require('../../../utils/route.js');

const app = getApp();

/**
 * 基础搜索行为
 */
const BaseSearchBehavior = Behavior({
  properties: {
    wrapHeight: String, // 容器高度
    isPage: <PERSON><PERSON><PERSON>, // 用于设置所属行业样式
    filterAry: {
      // 过滤数组（暂时未使用）
      type: Array,
      value: []
    }
  },

  data: {
    isIphoneX: app.globalData.isIphoneX,
    params: DEFAULT_PARAMS,
    login: app.globalData.login,

    // 输入框状态
    minCapital: '', // 最低资本
    maxCapital: '', // 最高资本
    dateType: '', // 日期类型（开始/结束）
    dateActive: false, // 日期输入框活动状态
    date: '', // 日期组件回填数据
    capitalActive: false, // 注册资本输入框活动状态
    minDate: '', // 最低年限
    maxDate: '', // 最高年限
    socialActive: false, // 社保输入框活动状态
    socialminPeson: '', // 最低社保人数
    socialmaxPeson: '', // 最高社保人数

    // 弹窗状态
    searPop: false,
    saveSearPop: false,
    regionPop: false,
    datePop: false,
    eleseicPop: false,
    enttypePop: false,
    districtPop: false,
    chainCodePop: false,

    // 其他状态
    idName: '', // 滚动到id名字的位置
    focus: false, // 企业名称输入框焦点状态
    searchList: [], // 搜索建议列表
    templateAry: [],
    renderAry: [] // 模版渲染数组
  },

  observers: {
    params: function (val) {
      this.result(val);
    }
  },

  methods: {
    /**
     * 清空搜索条件
     */
    clearSear() {
      const resetStates = DataProcessor.resetExtraStates();
      const resetParams = DataProcessor.resetParams(DEFAULT_PARAMS);

      // 保持展开状态
      const list = this.getFilteredRenderList().map(item => {
        if (item.isOpenIcon) {
          item.isOpen = this.data.itemList.filter(
            i => item.type === i.type
          )[0]?.isOpen;
        }
        return item;
      });

      this.setData({
        itemList: JSON.parse(JSON.stringify(list)),
        params: resetParams,
        ...resetStates
      });
    },

    /**
     * 获取过滤后的渲染列表（子类需要实现）
     */
    getFilteredRenderList() {
      throw new Error(
        'getFilteredRenderList method must be implemented by subclass'
      );
    },

    /**
     * 设置回填数据
     * @param {Object} tempObj - 回填数据对象
     */
    setBackfillData(tempObj = {}) {
      this.clearSear();

      let {itemList, params} = this.data;
      let resetStates = DataProcessor.resetExtraStates();

      params = Object.assign(params, tempObj);

      // 处理回填逻辑
      itemList = this.processBackfillData(itemList, params, resetStates);

      this.setData({
        params,
        itemList,
        ...resetStates
      });
    },

    /**
     * 处理回填数据
     * @param {Array} itemList - 项目列表
     * @param {Object} params - 参数对象
     * @param {Object} resetStates - 重置状态
     * @returns {Array} 处理后的项目列表
     */
    processBackfillData(itemList, params, resetStates) {
      return itemList.map(item => {
        for (let key in params) {
          if (item.type === key) {
            const strategy = SearchStrategyFactory.getStrategy(key);
            const backfillResult = strategy.handleBackfill(
              params,
              key,
              params[key]
            );

            // 处理特殊的回填逻辑
            this.handleSpecialBackfill(item, key, backfillResult, resetStates);

            // 设置弹窗内容
            this.setPopContent(item, key, params);

            // 设置标签状态
            this.setTagStates(item, key, backfillResult.tagId);
          }
        }
        return item;
      });
    },

    /**
     * 处理特殊回填逻辑
     * @param {Object} item - 项目对象
     * @param {string} key - 字段名
     * @param {Object} backfillResult - 回填结果
     * @param {Object} resetStates - 重置状态
     */
    handleSpecialBackfill(item, key, backfillResult, resetStates) {
      if (backfillResult.customInput) {
        switch (key) {
          case 'register_capital':
            resetStates.minCapital = backfillResult.customInput.min;
            resetStates.maxCapital = backfillResult.customInput.max;
            resetStates.capitalActive = backfillResult.customInput.active;
            break;
          case 'super_dimension_social_num':
            resetStates.socialminPeson = backfillResult.customInput.min;
            resetStates.socialmaxPeson = backfillResult.customInput.max;
            resetStates.socialActive = backfillResult.customInput.active;
            break;
          case 'register_time':
            resetStates.minDate = backfillResult.customInput.min;
            resetStates.maxDate = backfillResult.customInput.max;
            resetStates.dateActive = backfillResult.customInput.active;
            break;
        }
      }
    },

    /**
     * 设置弹窗内容
     * @param {Object} item - 项目对象
     * @param {string} key - 字段名
     * @param {Object} params - 参数对象
     */
    setPopContent(item, key, params) {
      const popFieldsMap = {
        areas: 'regionData',
        trade_types: 'eleseic_data',
        ent_type: 'enttype_data',
        ent_cert: 'all_cert_data',
        chain_codes: 'chain_codes_data'
      };

      if (popFieldsMap[key]) {
        const dataKey = popFieldsMap[key];
        const obj = params[dataKey];
        if (obj?.length > 0) {
          item.content = DataProcessor.getNameFromPop(obj);
        }
      }
    },

    /**
     * 设置标签状态
     * @param {Object} item - 项目对象
     * @param {string} key - 字段名
     * @param {*} tagId - 标签ID
     */
    setTagStates(item, key, tagId) {
      if (!item.list) return;

      item.list = item.list.map(tag => {
        const isArray = Array.isArray(tagId);

        if (isArray) {
          tagId.forEach(id => {
            if (tag.id === id) {
              tag.active = true;
            }
          });
        } else {
          if (tag.id === tagId) {
            tag.active = true;
          }
        }

        return tag;
      });
    },

    /**
     * 选择标签
     * @param {Object} event - 事件对象
     */
    async selectTag(event) {
      const {id, type, name, item} = event.currentTarget.dataset;

      // VIP权限检查
      if (item?.vip && !(await this.checkVipPermission())) {
        return;
      }

      const strategy = SearchStrategyFactory.getStrategy(type);
      const result = strategy.handleTagSelect(
        this.data.params,
        type,
        id,
        this.data.itemList
      );

      // 处理清除输入框的情况
      if (result.clearInputs) {
        this.clearInputsByType(type);
      }

      this.setData({
        itemList: result.itemList,
        params: result.params,
        ...this.getUpdatedInputStates(type)
      });
    },

    /**
     * 检查VIP权限
     * @returns {boolean} 是否有权限
     */
    async checkVipPermission() {
      const str = await hasPrivile({packageType: true});

      if (str === '游客') {
        app.route(this, '/pages/login/login');
        return false;
      } else if (str === '普通VIP') {
        this.setData({vipVisible: true});
        return false;
      }

      return true;
    },

    /**
     * 根据类型清除输入框
     * @param {string} type - 字段类型
     */
    clearInputsByType(type) {
      const clearMap = {
        register_capital: {
          minCapital: '',
          maxCapital: '',
          capitalActive: false
        },
        super_dimension_social_num: {
          socialminPeson: '',
          socialmaxPeson: '',
          socialActive: false
        },
        register_time: {
          minDate: '',
          maxDate: '',
          dateActive: false
        }
      };

      const clearStates = clearMap[type];
      if (clearStates) {
        this.setData(clearStates);
      }
    },

    /**
     * 获取更新后的输入状态
     * @param {string} type - 字段类型
     * @returns {Object} 更新后的状态
     */
    getUpdatedInputStates(type) {
      const stateMap = {
        register_capital: {
          minCapital: this.data.minCapital,
          maxCapital: this.data.maxCapital,
          capitalActive: this.data.capitalActive
        },
        super_dimension_social_num: {
          socialminPeson: this.data.socialminPeson,
          socialmaxPeson: this.data.socialmaxPeson,
          socialActive: this.data.socialActive
        },
        register_time: {
          minDate: this.data.minDate,
          maxDate: this.data.maxDate,
          dateActive: this.data.dateActive
        }
      };

      return stateMap[type] || {};
    },

    /**
     * 输出搜索结果
     * @param {Object} val - 参数值
     */
    result(val) {
      const paramsData = val || this.data.params;
      const isHeight = DataProcessor.getHeightStatus(paramsData);
      const processedData = DataProcessor.handleData(paramsData);
      const finalData = JSON.parse(processedData);

      this.setData({
        paramsData: finalData,
        isHeight
      });

      this.triggerEvent('submit', {
        isHeight,
        paramsData: clone(finalData)
      });
    },

    /**
     * 输入框获取焦点时清除选中的标签
     * @param {Object} e - 事件对象
     */
    inputFocus(e) {
      this.setTagStatus(e.currentTarget.dataset.type);
    },

    /**
     * 输入框内容变化处理
     * @param {Object} e - 事件对象
     */
    inputChange(e) {
      const {minCapital, maxCapital, socialmaxPeson, socialminPeson} =
        this.data;
      const types = e.currentTarget.dataset.type;
      const type = 'params.' + types;

      if (types === 'super_dimension_social_num') {
        this.setData(
          {
            socialActive: socialminPeson !== '' || socialmaxPeson !== '',
            [type]: [
              {
                start: socialminPeson,
                end: socialmaxPeson,
                special: true
              }
            ]
          },
          () => this.result()
        );
      } else if (types === 'register_capital') {
        this.setData(
          {
            capitalActive: minCapital !== '' || maxCapital !== '',
            [type]: [
              {
                start: minCapital,
                end: maxCapital,
                special: true
              }
            ]
          },
          () => this.result()
        );
      }
    },

    /**
     * 设置标签状态为false
     * @param {string} type - 字段类型
     */
    setTagStatus(type) {
      let {itemList, params} = this.data;

      for (let item of itemList) {
        if (item.type === type) {
          item.list = item.list.map(tag => {
            tag.active = false;
            return tag;
          });
        }
      }

      params[type] = [];
      this.setData({itemList, params});
    },

    /**
     * 设置日期
     * @param {Object} event - 事件对象
     */
    setDate(event) {
      const {date} = event.detail;
      let {minDate, maxDate, dateType} = this.data;

      let name = '';
      if (date) {
        if (dateType === 'startDate') minDate = date;
        if (dateType === 'endDate') maxDate = date;
      }

      if (minDate) name = '最低年限' + minDate;
      if (maxDate) name += '最高年限' + maxDate;

      this.setTagStatus('register_time');

      this.setData(
        {
          minDate,
          maxDate,
          dateActive: minDate || maxDate,
          'params.register_time': [
            {
              start: minDate,
              end: maxDate,
              special: true,
              name
            }
          ]
        },
        () => this.result()
      );
    },

    /**
     * 显示日期选择器
     * @param {Object} event - 事件对象
     */
    showtDatePicker(event) {
      const {type} = event.currentTarget.dataset;
      let {date, minDate, maxDate} = this.data;

      date = type === 'startDate' ? minDate : maxDate;

      this.setData({
        datePop: true,
        dateType: type,
        date
      });
    },

    /**
     * 多选弹窗回调
     * @param {Object} e - 事件对象
     */
    submitSub(e) {
      const {itemList} = this.data;
      const {checkedList: obj, mark} = e.detail;

      const popFieldsMap = {
        areas: 'regionData',
        trade_types: 'eleseic_data',
        ent_type: 'enttype_data',
        ent_cert: 'all_cert_data',
        chain_codes: 'chain_codes_data'
      };

      const dataKey = popFieldsMap[mark];
      if (!dataKey || obj.length < 0) return;

      const name = DataProcessor.getNameFromPop(obj);
      itemList.forEach(item => {
        if (item.type === mark) {
          item.content = name;
        }
      });

      this.setData(
        {
          [`params.${dataKey}`]: obj,
          itemList,
          chainCodePop: false,
          enttypePop: false,
          regionPop: false,
          eleseicPop: false
        },
        () => this.result()
      );
    },

    /**
     * 处理弹窗显示
     * @param {Object} e - 事件对象
     */
    handlePop(e) {
      const {
        item: {title}
      } = e.currentTarget.dataset;

      const popMap = {
        所在地区: {regionPop: true},
        所属行业: {eleseicPop: true},
        企业类型: {enttypePop: true},
        企业许可: {districtPop: true},
        所属产业链: {chainCodePop: true}
      };

      const popState = popMap[title];
      if (popState) {
        this.setData(popState);
      }
    },

    /**
     * 企业名称输入处理
     * @param {Object} e - 事件对象
     */
    onInput(e) {
      const value = e.detail.value;

      this.setData(
        {
          'params.ent_name': value
        },
        () => {
          this.getSearchList(value);
          this.result();
        }
      );
    },

    /**
     * 获取搜索建议列表
     */
    getSearchList: debounce(function ([...val]) {
      const value = val[0].toString();

      this.setData({searchList: []});

      common.getNameList(value).then(res => {
        if (res.length <= 0) {
          this.setData({text: '请输入更精确的内容!'});
          return;
        }

        const data = handleSearchHight(
          res,
          'ent_name',
          this.data.params.ent_name
        );
        this.setData({searchList: data});
      });
    }, 1000),

    /**
     * 点击搜索建议项
     * @param {Object} e - 事件对象
     */
    clickItem(e) {
      const {ent_name} = e.currentTarget.dataset.item;
      const str = ent_name.join('');

      this.setData(
        {
          'params.ent_name': str
        },
        () => {
          this.setData({focus: false});
          this.result();
        }
      );
    },

    /**
     * 输入框焦点处理
     */
    onFocus() {
      this.setData({focus: true});
    },

    /**
     * 关闭输入弹窗
     */
    closeInptPop() {
      this.setData({focus: false});
    },

    /**
     * 登录检查
     */
    checkLogin: hijack(function () {}, {
      app,
      type: 'huntSear'
    }),

    /**
     * 左侧分类展开/收起
     * @param {Object} e - 事件对象
     */
    closeleft(e) {
      let {leftList} = this.data;
      const {
        item: {title, isOpen}
      } = e.currentTarget.dataset;

      leftList.some(item => {
        if (item.title === title) {
          item.isOpen = !isOpen;
          return true;
        }
      });

      this.setData({leftList});
    },

    /**
     * 左侧分类激活
     * @param {Object} e - 事件对象
     */
    leftactvie(e) {
      let {leftList, idName} = this.data;
      const {
        item: {title},
        itm
      } = e.currentTarget.dataset;

      leftList.some(item => {
        if (item.onlyText) {
          item.isActive = false;
          item.map.forEach(i => (i.active = false));
        }

        if (item.title === title) {
          item.map.forEach(i => {
            if (i.key === itm) {
              i.active = true;
              item.isActive = true;
              idName = i.key;
            }
          });
        }
      });

      this.setData({leftList, idName});
    },

    /**
     * 右侧项目展开/收起
     * @param {Object} e - 事件对象
     */
    openright(e) {
      const {
        item: {type, isOpen}
      } = e.currentTarget.dataset;
      let {itemList} = this.data;

      itemList.some(i => {
        if (i.type === type) {
          i.isOpen = !isOpen;
          return true;
        }
      });

      this.setData({itemList});
    }
  }
});

module.exports = BaseSearchBehavior;
