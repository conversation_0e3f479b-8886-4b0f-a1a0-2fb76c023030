/**
 * 搜索策略类
 * 使用策略模式处理不同类型的搜索逻辑
 */

const {
  RADIO_FIELDS,
  MULTI_SELECT_FIELDS,
  RANGE_INPUT_FIELDS,
  DATE_POP_FIELDS,
  BOOLEAN_FIELDS,
  PARENT_TYPE_PREFIXES
} = require('./constants.js');

/**
 * 基础搜索策略
 */
class BaseSearchStrategy {
  constructor() {
    this.type = 'base';
  }

  /**
   * 处理标签选择
   * @param {Object} params - 参数对象
   * @param {string} type - 字段类型
   * @param {string} id - 选中的ID
   * @param {Array} itemList - 项目列表
   * @returns {Object} 更新后的参数和列表
   */
  handleTagSelect(params, type, id, itemList) {
    throw new Error('handleTagSelect method must be implemented');
  }

  /**
   * 验证输入
   * @param {Object} paramsData - 参数数据
   * @returns {boolean} 验证结果
   */
  validateInput(paramsData) {
    return true;
  }

  /**
   * 处理回填数据
   * @param {Object} params - 参数对象
   * @param {string} type - 字段类型
   * @param {*} value - 回填值
   * @returns {Object} 处理后的数据
   */
  handleBackfill(params, type, value) {
    return {tagId: value};
  }
}

/**
 * 单选搜索策略
 */
class RadioSearchStrategy extends BaseSearchStrategy {
  constructor() {
    super();
    this.type = 'radio';
  }

  handleTagSelect(params, type, id, itemList) {
    const updatedItemList = itemList.map(item => {
      if (item.type === type) {
        item.list = item.list.map(tag => {
          tag.active = false; // 先清除所有选中状态
          if (tag.id === id) {
            if (params[type] == id) {
              tag.active = false;
              params[type] = [];
            } else {
              tag.active = true;
              params[type] = [id];
            }
          }
          return tag;
        });
      }
      return item;
    });

    return {params, itemList: updatedItemList};
  }

  handleBackfill(params, type, value) {
    return {tagId: value};
  }
}

/**
 * 多选搜索策略
 */
class MultiSelectSearchStrategy extends BaseSearchStrategy {
  constructor() {
    super();
    this.type = 'multiSelect';
  }

  handleTagSelect(params, type, id, itemList) {
    const updatedItemList = itemList.map(item => {
      if (item.type === type) {
        item.list = item.list.map(tag => {
          if (tag.id === id) {
            const arr = params[type];
            if (arr.includes(id)) {
              arr.splice(
                arr.findIndex(i => i === id),
                1
              );
              tag.active = false;
            } else {
              arr.push(id);
              tag.active = true;
            }
          }
          return tag;
        });
      }
      return item;
    });

    return {params, itemList: updatedItemList};
  }

  handleBackfill(params, type, value) {
    return {tagId: value};
  }
}

/**
 * 范围输入搜索策略
 */
class RangeInputSearchStrategy extends BaseSearchStrategy {
  constructor() {
    super();
    this.type = 'rangeInput';
  }

  handleTagSelect(params, type, id, itemList) {
    const obj = this.divisionStr(id);

    // 清除自定义输入的数据
    params[type] = params[type].filter(i => !i.special);

    const updatedItemList = itemList.map(item => {
      if (item.type === type) {
        item.list = item.list.map(tag => {
          if (tag.id === id) {
            const arr = params[type];
            const idx = arr.findIndex(
              i => JSON.stringify(i) === JSON.stringify(obj)
            );

            if (idx >= 0) {
              arr.splice(idx, 1);
              tag.active = false;
            } else {
              arr.push(obj);
              tag.active = true;
            }
          }
          return tag;
        });
      }
      return item;
    });

    return {
      params,
      itemList: updatedItemList,
      clearInputs: true // 标记需要清除输入框
    };
  }

  /**
   * 分割字符串
   * @param {string} str - 待分割的字符串
   * @returns {Object} 分割后的对象
   */
  divisionStr(str) {
    const sign = '$';
    const obj = {start: '', end: ''};

    if (str.indexOf(sign) > -1) {
      const arr = str.split(sign);
      obj.start = arr[0];
      obj.end = arr[1];
    }

    return obj;
  }

  /**
   * 处理自定义输入
   * @param {Object} params - 参数对象
   * @param {string} type - 字段类型
   * @param {string} start - 开始值
   * @param {string} end - 结束值
   * @returns {Object} 更新后的参数
   */
  handleCustomInput(params, type, start, end) {
    params[type] = [
      {
        start,
        end,
        special: true
      }
    ];
    return params;
  }

  validateInput(paramsData) {
    const fields = RANGE_INPUT_FIELDS.list;

    return !Object.keys(paramsData).some(key => {
      if (fields.includes(key) && paramsData[key].length === 1) {
        const item = paramsData[key][0];
        if (item.special && parseFloat(item.start) >= parseFloat(item.end)) {
          const config = RANGE_INPUT_FIELDS.config[key];
          wx.showToast({
            title: `${config.min}不能大于等于${config.max}`,
            icon: 'none'
          });
          return true;
        }
      }
      return false;
    });
  }

  handleBackfill(params, type, value) {
    if (!value.length) return {tagId: value};

    const str = value[0].start + '$' + value[0].end;
    const config = RANGE_INPUT_FIELDS.config[type];
    const predefinedValues = this.getPredefinedValues(type);

    if (!predefinedValues.includes(str)) {
      return {
        tagId: str,
        customInput: {
          min: value[0].start,
          max: value[0].end,
          active: true
        }
      };
    }

    return {tagId: value};
  }

  /**
   * 获取预定义值列表
   * @param {string} type - 字段类型
   * @returns {Array} 预定义值列表
   */
  getPredefinedValues(type) {
    const predefinedMap = {
      register_capital: ['0$100', '100$200', '500$1000', '1000$5000', '5000$'],
      super_dimension_social_num: [
        '0$49',
        '50$99',
        '100$499',
        '500$999',
        '1000$4999',
        '5000$'
      ]
    };

    return predefinedMap[type] || [];
  }
}

/**
 * 日期弹窗搜索策略
 */
class DatePopSearchStrategy extends RangeInputSearchStrategy {
  constructor() {
    super();
    this.type = 'datePop';
  }

  validateInput(paramsData) {
    const fields = DATE_POP_FIELDS.list;

    return !Object.keys(paramsData).some(key => {
      if (fields.includes(key) && paramsData[key].length === 1) {
        const item = paramsData[key][0];
        if (item.special) {
          const startTime = new Date(item.start).getTime();
          const endTime = new Date(item.end).getTime();

          if (startTime >= endTime) {
            const config = DATE_POP_FIELDS.config[key];
            wx.showToast({
              title: `${config.min}不能大于等于${config.max}`,
              icon: 'none'
            });
            return true;
          }
        }
      }
      return false;
    });
  }

  handleBackfill(params, type, value) {
    if (!value.length) return {tagId: value};

    const str = value[0].start + '$' + value[0].end;
    // 这里需要从外部传入 dateTagList
    const dateTagList = this.getDateTagList();

    if (!dateTagList.includes(str)) {
      return {
        tagId: str,
        customInput: {
          min: value[0].start,
          max: value[0].end,
          active: true
        }
      };
    }

    return {tagId: value};
  }

  /**
   * 获取日期标签列表（需要从外部注入）
   * @returns {Array} 日期标签列表
   */
  getDateTagList() {
    // 这个方法需要从外部注入dateTagList
    return [];
  }
}

/**
 * 搜索策略工厂
 */
class SearchStrategyFactory {
  static strategies = {
    radio: new RadioSearchStrategy(),
    multiSelect: new MultiSelectSearchStrategy(),
    rangeInput: new RangeInputSearchStrategy(),
    datePop: new DatePopSearchStrategy()
  };

  /**
   * 获取搜索策略
   * @param {string} type - 字段类型
   * @returns {BaseSearchStrategy} 搜索策略实例
   */
  static getStrategy(type) {
    if (RADIO_FIELDS.list.includes(type)) {
      return this.strategies.radio;
    }

    if (MULTI_SELECT_FIELDS.list.includes(type)) {
      return this.strategies.multiSelect;
    }

    if (RANGE_INPUT_FIELDS.list.includes(type)) {
      return this.strategies.rangeInput;
    }

    if (DATE_POP_FIELDS.list.includes(type)) {
      return this.strategies.datePop;
    }

    return new BaseSearchStrategy();
  }

  /**
   * 设置日期标签列表
   * @param {Array} dateTagList - 日期标签列表
   */
  static setDateTagList(dateTagList) {
    this.strategies.datePop.getDateTagList = () => dateTagList;
  }
}

module.exports = {
  BaseSearchStrategy,
  RadioSearchStrategy,
  MultiSelectSearchStrategy,
  RangeInputSearchStrategy,
  DatePopSearchStrategy,
  SearchStrategyFactory
};
