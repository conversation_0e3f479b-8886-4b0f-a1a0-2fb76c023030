/**
 * 搜索组件工具函数
 * 简化的外部调用接口
 */

const DataProcessor = require('./dataProcessor.js');
const {getComponentConfig, addField, removeField} = require('./config.js');

/**
 * 清空子组件搜索条件
 * @param {Object} that - 页面实例
 * @param {string} idName - 组件ID选择器
 * @returns {Function} 清空函数
 */
function clearChildComponent(that, idName = '#hunt') {
  let child = that.selectComponent(idName);

  return function () {
    if (child) {
      child.clearSear();
    } else {
      child = that.selectComponent(idName);
      child?.clearSear();
    }
  };
  // 使用方法: clearChildComponent(this)()
}

/**
 * 回填子组件数据
 * @param {Object} that - 页面实例
 * @param {string} idName - 组件ID选择器
 * @returns {Function} 回填函数
 */
function fillChildComponent(that, idName = '#hunt') {
  let child = that.selectComponent(idName);

  return function (tempObj) {
    if (child) {
      child.setBackfillData(tempObj);
    } else {
      child = that.selectComponent(idName);
      child.setBackfillData(tempObj);
    }
  };
  // 使用方法: fillChildComponent(this)(tempObj)
}

/**
 * 关闭企业名称弹窗
 * @param {Object} that - 页面实例
 * @param {string} idName - 组件ID选择器
 * @returns {Function} 关闭函数
 */
function closeEntNamePop(that, idName = '#hunt') {
  let child = that.selectComponent(idName);

  return function () {
    if (child) {
      child.closeInptPop();
    } else {
      child = that.selectComponent(idName);
      child?.closeInptPop();
    }
  };
}

/**
 * 验证搜索参数
 * @param {Object} paramsData - 搜索参数
 * @returns {boolean} 验证结果
 */
function checkoutSear(paramsData = {}) {
  return DataProcessor.validateSearchParams(paramsData);
}

/**
 * 处理搜索数据
 * @param {Object} params - 搜索参数
 * @returns {string} 处理后的JSON字符串
 */
function handleData(params) {
  return DataProcessor.handleData(params);
}

/**
 * 获取弹窗数据的显示名称
 * @param {Array} data - 弹窗数据
 * @returns {string} 显示名称
 */
function getNameFromPop(data) {
  return DataProcessor.getNameFromPop(data);
}

/**
 * 处理数据结构
 * @param {Object} data - 原始数据
 * @returns {Object} 处理后的数据
 */
function handlestructure(data) {
  return DataProcessor.handleStructure(data);
}

/**
 * 获取高亮状态
 * @param {Object} paramsData - 参数数据
 * @returns {boolean} 是否有高亮
 */
function getHeightStatus(paramsData) {
  return DataProcessor.getHeightStatus(paramsData);
}

/**
 * 处理多选数据
 * @param {Array} ary - 原始数组
 * @returns {Array} 处理后的数组
 */
function handleMultiple(ary = []) {
  return DataProcessor.handleMultiple(ary);
}

/**
 * 动态添加筛选字段
 * @param {string} type - 字段类型 ('input'|'radio'|'multiSelect'|'rangeInput'|'datePop'|'pop')
 * @param {string} key - 字段键名
 * @param {Object} config - 字段配置
 * @example
 * addSearchField('input', 'company_code', {
 *   label: '企业代码',
 *   placeholder: '请输入企业代码'
 * });
 */
function addSearchField(type, key, config) {
  addField(type, key, config);
}

/**
 * 移除筛选字段
 * @param {string} key - 字段键名
 */
function removeSearchField(key) {
  removeField(key);
}

/**
 * 获取组件配置
 * @param {string} componentType - 组件类型
 * @returns {Object} 组件配置
 */
function getSearchComponentConfig(componentType) {
  return getComponentConfig(componentType);
}

/**
 * 简单的参数验证
 * @param {Object} params - 搜索参数
 * @param {Array} requiredFields - 必填字段列表
 * @returns {Object} 验证结果
 */
function validateSearchParams(params, requiredFields = []) {
  const missingFields = [];

  requiredFields.forEach(field => {
    if (
      !params[field] ||
      (Array.isArray(params[field]) && params[field].length === 0) ||
      (typeof params[field] === 'string' && params[field].trim() === '')
    ) {
      missingFields.push(field);
    }
  });

  return {
    isValid: missingFields.length === 0,
    missingFields,
    message:
      missingFields.length > 0
        ? `缺少必填字段: ${missingFields.join(', ')}`
        : '验证通过'
  };
}

/**
 * 生成搜索摘要
 * @param {Object} params - 搜索参数
 * @returns {string} 搜索摘要
 */
function generateSearchSummary(params) {
  const summary = [];

  if (params.ent_name) {
    summary.push(`企业名称: ${params.ent_name}`);
  }

  if (params.regionData?.length > 0) {
    const regionNames = DataProcessor.getNameFromPop(params.regionData);
    summary.push(`地区: ${regionNames}`);
  }

  if (params.eleseic_data?.length > 0) {
    const industryNames = DataProcessor.getNameFromPop(params.eleseic_data);
    summary.push(`行业: ${industryNames}`);
  }

  return summary.length > 0 ? summary.join(', ') : '无搜索条件';
}

module.exports = {
  // 基础工具函数
  clearChildComponent,
  fillChildComponent,
  closeEntNamePop,
  checkoutSear,
  handleData,
  getNameFromPop,
  handlestructure,
  getHeightStatus,
  handleMultiple,

  // 配置管理
  addSearchField,
  removeSearchField,
  getSearchComponentConfig,

  // 验证和处理
  validateSearchParams,
  generateSearchSummary
};
