/**
 * 搜索组件工具函数
 * 提供外部调用的便捷方法
 */

const DataProcessor = require('./dataProcessor.js');

/**
 * 清空子组件搜索条件
 * @param {Object} that - 页面实例
 * @param {string} idName - 组件ID选择器
 * @returns {Function} 清空函数
 */
function clearChildComponent(that, idName = '#hunt') {
  let child = that.selectComponent(idName);

  return function () {
    if (child) {
      child.clearSear();
    } else {
      child = that.selectComponent(idName);
      child?.clearSear();
    }
  };
  // 使用方法: clearChildComponent(this)()
}

/**
 * 回填子组件数据
 * @param {Object} that - 页面实例
 * @param {string} idName - 组件ID选择器
 * @returns {Function} 回填函数
 */
function fillChildComponent(that, idName = '#hunt') {
  let child = that.selectComponent(idName);

  return function (tempObj) {
    if (child) {
      child.setBackfillData(tempObj);
    } else {
      child = that.selectComponent(idName);
      child.setBackfillData(tempObj);
    }
  };
  // 使用方法: fillChildComponent(this)(tempObj)
}

/**
 * 关闭企业名称弹窗
 * @param {Object} that - 页面实例
 * @param {string} idName - 组件ID选择器
 * @returns {Function} 关闭函数
 */
function closeEntNamePop(that, idName = '#hunt') {
  let child = that.selectComponent(idName);

  return function () {
    if (child) {
      child.closeInptPop();
    } else {
      child = that.selectComponent(idName);
      child?.closeInptPop();
    }
  };
}

/**
 * 验证搜索参数
 * @param {Object} paramsData - 搜索参数
 * @returns {boolean} 验证结果
 */
function checkoutSear(paramsData = {}) {
  return DataProcessor.validateSearchParams(paramsData);
}

/**
 * 处理搜索数据
 * @param {Object} params - 搜索参数
 * @returns {string} 处理后的JSON字符串
 */
function handleData(params) {
  return DataProcessor.handleData(params);
}

/**
 * 获取弹窗数据的显示名称
 * @param {Array} data - 弹窗数据
 * @returns {string} 显示名称
 */
function getNameFromPop(data) {
  return DataProcessor.getNameFromPop(data);
}

/**
 * 处理数据结构
 * @param {Object} data - 原始数据
 * @returns {Object} 处理后的数据
 */
function handlestructure(data) {
  return DataProcessor.handleStructure(data);
}

/**
 * 获取高亮状态
 * @param {Object} paramsData - 参数数据
 * @returns {boolean} 是否有高亮
 */
function getHeightStatus(paramsData) {
  return DataProcessor.getHeightStatus(paramsData);
}

/**
 * 处理多选数据
 * @param {Array} ary - 原始数组
 * @returns {Array} 处理后的数组
 */
function handleMultiple(ary = []) {
  return DataProcessor.handleMultiple(ary);
}

/**
 * 搜索组件配置工厂
 * 根据组件类型返回相应的配置
 */
class SearchComponentFactory {
  /**
   * 创建搜索组件配置
   * @param {string} type - 组件类型 ('hunt' | 'huntCopy')
   * @param {Array} renderList - 原始渲染列表
   * @returns {Object} 组件配置
   */
  static createConfig(type, renderList) {
    const filteredList = DataProcessor.filterDataByComponent(renderList, type);

    return {
      type,
      itemList: JSON.parse(JSON.stringify(filteredList)),
      leftList: JSON.parse(JSON.stringify(filteredList)),
      getFilteredRenderList: () => filteredList
    };
  }

  /**
   * 获取组件差异配置
   * @param {string} type - 组件类型
   * @returns {Object} 差异配置
   */
  static getDifferenceConfig(type) {
    const configs = {
      hunt: {
        description: '完整的企业搜索组件，包含所有搜索字段',
        excludeFields: [],
        features: ['产业链搜索', '完整字段支持']
      },
      huntCopy: {
        description: '简化的企业搜索组件，排除产业链相关字段',
        excludeFields: ['chain_codes'],
        features: ['基础搜索', '排除产业链']
      }
    };

    return configs[type] || {};
  }
}

/**
 * 搜索参数验证器
 */
class SearchParamsValidator {
  /**
   * 验证必填字段
   * @param {Object} params - 搜索参数
   * @param {Array} requiredFields - 必填字段列表
   * @returns {Object} 验证结果
   */
  static validateRequired(params, requiredFields = []) {
    const missingFields = [];

    requiredFields.forEach(field => {
      if (
        !params[field] ||
        (Array.isArray(params[field]) && params[field].length === 0) ||
        (typeof params[field] === 'string' && params[field].trim() === '')
      ) {
        missingFields.push(field);
      }
    });

    return {
      isValid: missingFields.length === 0,
      missingFields,
      message:
        missingFields.length > 0
          ? `缺少必填字段: ${missingFields.join(', ')}`
          : '验证通过'
    };
  }

  /**
   * 验证字段格式
   * @param {Object} params - 搜索参数
   * @returns {Object} 验证结果
   */
  static validateFormat(params) {
    const errors = [];

    // 验证企业名称长度
    if (params.ent_name && params.ent_name.length > 50) {
      errors.push('企业名称长度不能超过50个字符');
    }

    // 验证数值范围
    ['register_capital', 'super_dimension_social_num'].forEach(field => {
      if (params[field] && Array.isArray(params[field])) {
        params[field].forEach(item => {
          if (item.start && item.end) {
            const start = parseFloat(item.start);
            const end = parseFloat(item.end);

            if (isNaN(start) || isNaN(end)) {
              errors.push(`${field} 数值格式不正确`);
            } else if (start >= end) {
              errors.push(`${field} 起始值不能大于等于结束值`);
            }
          }
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      message: errors.length > 0 ? errors.join('; ') : '格式验证通过'
    };
  }

  /**
   * 综合验证
   * @param {Object} params - 搜索参数
   * @param {Array} requiredFields - 必填字段列表
   * @returns {Object} 验证结果
   */
  static validate(params, requiredFields = []) {
    const requiredResult = this.validateRequired(params, requiredFields);
    const formatResult = this.validateFormat(params);

    return {
      isValid: requiredResult.isValid && formatResult.isValid,
      requiredResult,
      formatResult,
      message: !requiredResult.isValid
        ? requiredResult.message
        : !formatResult.isValid
        ? formatResult.message
        : '验证通过'
    };
  }
}

/**
 * 搜索结果处理器
 */
class SearchResultProcessor {
  /**
   * 格式化搜索结果
   * @param {Object} params - 搜索参数
   * @returns {Object} 格式化后的结果
   */
  static formatResult(params) {
    const processedData = DataProcessor.handleData(params);
    const structuredData = DataProcessor.handleStructure(
      JSON.parse(processedData)
    );

    return {
      original: params,
      processed: JSON.parse(processedData),
      structured: structuredData,
      isHighlighted: DataProcessor.getHeightStatus(params)
    };
  }

  /**
   * 生成搜索摘要
   * @param {Object} params - 搜索参数
   * @returns {string} 搜索摘要
   */
  static generateSummary(params) {
    const summary = [];

    if (params.ent_name) {
      summary.push(`企业名称: ${params.ent_name}`);
    }

    if (params.regionData?.length > 0) {
      const regionNames = DataProcessor.getNameFromPop(params.regionData);
      summary.push(`地区: ${regionNames}`);
    }

    if (params.eleseic_data?.length > 0) {
      const industryNames = DataProcessor.getNameFromPop(params.eleseic_data);
      summary.push(`行业: ${industryNames}`);
    }

    return summary.length > 0 ? summary.join(', ') : '无搜索条件';
  }
}

// 导出所有工具函数和类
module.exports = {
  clearChildComponent,
  fillChildComponent,
  closeEntNamePop,
  checkoutSear,
  handleData,
  getNameFromPop,
  handlestructure,
  getHeightStatus,
  handleMultiple,
  SearchComponentFactory,
  SearchParamsValidator,
  SearchResultProcessor
};
