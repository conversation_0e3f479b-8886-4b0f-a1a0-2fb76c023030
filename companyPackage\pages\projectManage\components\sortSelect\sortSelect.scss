/* companyPackage/pages/projectManage/components/sortSelect.scss */
.select-item-box {
  max-height: 480rpx;
  overflow-y: auto;
  padding: 0 24rpx;
}

.select-item {
  height: 96rpx;
  color: #74798C;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.select-item .textFont {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 33rpx;
}

.select-item.active .textFont {
  color: #E72410 !important;
}
.select-item.active .select {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAATlJREFUWEftlj1uwjAYhl8DGwi8komiMLGyU4aeoBfgBqh7pa6MHIKBWyBuwAGQMAONOlSy1KVDEqMgWQpJIP6hSSWS2dHz+P38+TNByR8pmY9KoErg8RLwXDru7vhGdl+hCXz26YzUsBDAwtnxt0iiMAEJP+9cgDV/g1H7+PNdiEASjgCTLuOskARuwf9cIA+eKbDvUfrEOLedESrwlIDn0g8ITEWIZ2fPD6YSqvALAc9tDYH6FiCN6JSaSujAUwl8DTqvocDKVEIXnnkGTCVM4Fe7QFfCFH6zDVUlbOC590CehC08VyBacE3iHnAlgSwJAiwFwbscLPG7XffuUB5GF0lIigCzgSsnIHnJctjCtQVi5ZjDJy9ypOrGHl+vXIL4T2ugMQF8G3ApT7IsYaME7rHzKoF/k8AJqsP2IbiZ1dsAAAAASUVORK5CYII=') no-repeat center center;
  background-size: 100% 100%;
}

.select-item.active {
  font-weight: 600;
  color: #E72410;
}

.select-item:not(:first-child)::before {
  content: " ";
  height: 2rpx;
  width: 100%;
  background: #EEEEEE;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}