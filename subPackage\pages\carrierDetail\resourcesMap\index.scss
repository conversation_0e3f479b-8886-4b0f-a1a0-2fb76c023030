/* subPackage/pages/carrierDetail/resourcesMap/index.scss */
/* 单独抽离样式 */
cover-view {
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.cover-view-wrapper {
  width: max-content;
  max-width: 400rpx;
  height: auto;
  /* max-height: 80rpx; */
  padding: 16rpx 20rpx;
  background: #E72410;
  color: #fff;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}

/* 主体样式 */
.resources-map {
  width: 100%;
  height: 100%;
}

.resources-map .console-tab-wrapper .console-tabs {
  width: 100%;
  display: flex;
  justify-self: flex-start;
  align-items: center;
}

.resources-map .console-tab-wrapper .tab-item {
  width: 20%;
  line-height: 64rpx;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
  background: #F5F4F4;
  box-sizing: border-box;
}

.resources-map .console-tab-wrapper .tab-item.active {
  background: #fff;
  border-top: 4rpx solid #E72410;

}

.resources-map .console-tab-wrapper .content-wrapper {
  padding: 16rpx 24rpx;
  max-height: 360rpx;
  overflow: scroll;
}

.content-wrapper .content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
}

.content-wrapper .content-item view {
  min-width: 140rpx;
  text-align: right;
  line-height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.content-wrapper .content-item text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}