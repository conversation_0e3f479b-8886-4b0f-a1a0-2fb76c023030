@import '../../../../template/null/null.scss';

.page {
  width: 100%;
  height: 100vh;
  scroll-behavior: auto;
  overflow-y: scroll;
  background-color: #F7F7F7;
  position: relative;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.head_bg {
  width: 100%;
  height:200rpx;
  position: absolute;
  top: 0;
  z-index: 1;
}
/* 头部 */
.header-box {
  position: absolute;
  top: 66rpx;
  display: flex;
  width: 750rpx;
  min-height: 344rpx;
  padding: 40rpx 24rpx;
  background: #FFFFFF;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  z-index: 2;
}
.page_content {
  position: absolute;
}
.header-box .left {
   /* 定宽 */
  width: 120rpx;
}
.header-box .left>image {
  width: 120rpx;
  height: 120rpx;
  margin-top: 0;
}
.header-box .right {
  margin-left: 20rpx;
  /* flex 属性是 flex-grow、flex-shrink 和 flex-basis 属性的简写属性。 */
  flex: 1;
}
.header-box .right .title, .header-box .right .name_tag {
  margin-bottom: 16rpx;
}
.collect-box {
  display: flex;
  justify-content: space-around;
}
.card_h_r_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  margin-top: 6rpx;
  margin-right: 4rpx;
}
.com_name {
  width: auto;
  height: 44rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}
.invest_round_name {
  margin-left: 20rpx;
  min-width: 236rpx;
  height: 36rpx;
  padding: 2rpx 10rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #26C8A7;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #26C8A7;
}
.header-box .right .title>text:first-child {
  width: 140rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}
.header-box .right .title>text:last-child {
  width: auto;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.tag_box {
  /* display: flex; */
  min-height: 82rpx;
  overflow:hidden;

}
.tag_box_II {
  position: relative;
  text-overflow: clip;
  overflow:hidden;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.tag_box_II .tag_item {
  width: calc(100% - 20rpx);
}
.tag_item {
  width:100%;
  min-height: 82rpx;
  /* background-color: #1E75DB; */
}
.tag_box_more {
  position: absolute;
  right: -5rpx;
  bottom: 18rpx;
  background-color: #FFFFFF;
}
.tag_item>text {
  display: inline-block;
  width: auto;
  height: 36rpx;
  padding: 2rpx 10rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
}
.tag_item>text:nth-child(1n) {
  display: inline-block;
  width: auto;
  color: #1E75DB;
  background: rgba(30,117,219,0.1);
}
.tag_item>text:nth-child(2n) {
  color: #4AB8FF;
  background: rgba(74,184,255,0.1);
}
.tag_item>text:nth-child(3n)  {
  color: #FD9331;
  background: rgba(253,147,49,0.1);
}
.tag_item>text:nth-child(4n)  {
  color: #A0A5BA;
  background: rgba(160, 165, 186, 0.10);
}
.tag_item>text:nth-child(5n) {
  color: #FFB93E;
  background: rgba(255, 185, 62, 0.15)
}
.tag_item>text:nth-child(6n) {
  color: #9C85DB;
  background: rgba(156, 133, 219, 0.10);
}
.tag_box>view:last-child {
  display: inline;
}
.dialog_content {
  margin-bottom: 42rpx;
}
.red_right {
  display: none;
  width: 76rpx;
  color: #E72410;
  font-size: 24rpx;
  font-weight: 400;
  text-align: right;
}
.red_right>image {
  width: 20rpx;
  height: 20rpx;
}

/*  基本信息  */
.base_info {
  padding: 32rpx 24rpx;
}
.base_info>view {
  display: flex;
  margin-bottom: 16rpx;
}
.base_info>view>text:first-child {
  width: 140rpx;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  flex-shrink: 0;
}
.base_info>view>text:last-child {
  display: inline-block;
  margin-left: 28rpx;
  /* flex 属性是 flex-grow、flex-shrink 和 flex-basis 属性的简写属性。 */
  flex: 1;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}
.link {
  color:  #1E75DB !important;
}
.website {
  /* display: flex; */
  height: 40rpx;
  /* align-items: center; */
  font-size: 28rpx;
  margin-left: 18rpx;
  text-overflow: -o-ellipsis-lastline;
	overflow: hidden;		
	text-overflow: ellipsis;	
  display: flex;
  justify-content: center;
  align-items: center;		
	-webkit-line-clamp: 1;	
	line-clamp: 1;					
  -webkit-box-orient: vertical;
}
.website>text {
  display: inline-block;
  height: 40rpx;
}
.website_icon {
  width: 32rpx;
  height: 32rpx;
  flex-shrink: 0;
}

.history {
  padding: 40rpx 24rpx 40rpx 40rpx;
}
.history .list {
  width: 100%;
  min-height: 126rpx;
  padding-left: 34rpx;
  position: relative;
  /* background-color: orange; */
}
.history .list::before {
  position: absolute;
  content: '';
  left: 0rpx;
  top: 10rpx;
  width: 16rpx;
  height: 16rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAxklEQVQoU2NkYGBgeCbPo8nAwtTOwMDkCuIz/mfY+//vn3Kph1+uM0IkmY8yMjIKgiRh4P///+8Z/vy1ZnyuIrCJgYHBl93Vm4GvZQIDw79/DJ/qihh+7t7K8P//v42Mz5QFvjIyMnCJHr/JwCwiBjbg79s3DK8tVBn+/2f4BlcgdvI2A5OQCETBm1cMry3VYQr4NjAyMvmDrWjqY2BgZGL4VFPA8HPPVgaGfwybCTsS5k1GVpbO//8ZnCG++Leb4c+/SpA3Ad5NUyuZnpw8AAAAAElFTkSuQmCC') no-repeat;
  background-position: center;
}
.history .list::after {
  position: absolute;
  content: '';
  left: 7rpx;
  top: 26rpx;
  width: 2px;
  height: 100%;
  border-left: 2rpx dotted #DEDEDE;
}
.history .list .date {
  height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}


/* 相关资讯 */
.information {
  width: 100%;
  padding: 0 24rpx;
}
.information .list {
  width: 100%;
  height: 144rpx;
  padding: 32rpx 0;
  display: flex;
  border-bottom: 1rpx solid #EEEEEE;
}
.list_box >view:last-child {
  border-bottom: none !important;
}
.information .list .info_img {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.information .list .info_title {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  text-overflow: -o-ellipsis-lastline;
	overflow: hidden;		
	text-overflow: ellipsis;	
	display: -webkit-box;		
	-webkit-line-clamp: 2;	
	line-clamp: 2;					
  -webkit-box-orient: vertical;
}
.information .list .info_time {
  min-width: 130rpx;
  height: 34rpx;
  margin-left: 20rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}
.information  .more {
  position: relative;
  width: 492rpx;
  height: 116rpx;
  margin: 0rpx auto;
  padding: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
}
.information  .more::before {
  position: absolute;
  content: '';
  width: 160rpx;
  height: 1rpx;
  top: 50%;
  left: 0;
  background-color: rgba(231,36,16,0.16);
}
.information  .more::after {
  position: absolute;
  content: '';
  width: 160rpx;
  height: 1rpx;
  top: 50%;
  right: 0;
  background-color: rgba(231,36,16,0.16);
}
.more .red_right {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}