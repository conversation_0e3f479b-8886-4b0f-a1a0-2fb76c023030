@import '../../../../template/more/more.scss';
@import '../../../../template/null/null.scss';
@import '../../../../template/loading/index.scss';

.pub-page {
    position: relative;
    width: 100%;
    padding-bottom: 168rpx;
    box-sizing: border-box;
    height: 100vh;
}
::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }
/* input */
.input-box {
    padding: 28rpx 0;
    /* border-bottom: 20rpx solid #f7f7f7; */
}

.searchs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 24rpx 0;
    background: #eeeeee;
    border-radius: 8rpx;
    padding: 16rpx 0 16rpx 28rpx;
}


.s-input {
    display: flex;
    align-items: center;
    flex: 1;
}

.s-input-img {
    width: 40rpx;
    height: 40rpx;
}

input {
    caret-color: #E72410;
    color: #74798c;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
}

.s-input-item {
    display: flex;
    position: relative;
    flex: 1;
    height: 40rpx;
    padding-left: 16rpx;
    border-right: 1px solid hsla(229, 9%, 64%, 0.5);
    align-items: center;
}

.s-input-item-i {
    flex: 1;
    padding-right: 32rpx;
    position: relative;
    height: 40rpx;
    padding-right: 52rpx;
    align-items: center;

}

.placeholder {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #9b9eac;
    line-height: 40rpx;
}

.search-cancel {
    height: 40rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: RIGHT;
    color: #20263a;
    padding: 0 28rpx;
}

.input-clear {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    right: 4px;
    top: 0;
    width: 40rpx;
    height: 40rpx;
    z-index: 20;
}

/* 渲染列表 */
.item-list {
    display: flex;
    align-items: center;
    height: 96rpx;
    border-bottom: 1px solid #f7f7f7;
    padding: 0 24rpx;
}

.item-list-img {
    width: 40rpx;
    height: 40rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.textWrap {
    flex: 1;
    width: 622rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    flex-wrap: nowrap;
}

.item-list .text {
    display: inline;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #20263a;
}

/* 搜索高亮 */
.searchHigh {
    color: #E72410 !important;
}

.item-list-active {
    background-color: rgba(238, 238, 238, 0.5);
}

/* 地址弹窗 */

/* dialog文字样式 */
.dialog-con {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
}

/* 联系方式弹窗 */
.contact_box {
    position: relative;
    width: 100%;
    height: 96rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact_box::after {
    content: " ";
    width: 100%;
    height: 1px;
    background: #eee;
    position: absolute;
    bottom: 0;
    /* left: -50%; */
    transform: scaleY(0.5);
}

.contact_box:last-child::after {
    content: " ";
    width: 100%;
    height: 1px;
    background: transparent;
    position: absolute;
    bottom: 0;
    /* left: -50%; */
    transform: scaleY(0.5);
}

.contact_left {
    display: flex;
    align-items: end;
    padding: 28rpx 0;
}

.contact_left image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
}

.contact_number {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #E72410;
}

.contact_right {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
}