@import "../../../../../template/menuhead/index.scss";

.conent-box {
  position: relative;
}

.container {
  position: relative;
  z-index: 4;
  font-size: 14px;
}

.slidedown {
  transform: translateY(0%);
}

@keyframes slidown {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0%);
  }
}

.slidown {
  display: block;
  animation: slidown 0.2s ease-in both;
}

@keyframes slidup {
  from {
    transform: translateY(0%);
  }

  to {
    transform: translateY(-100%);
  }
}

.z-height {
  overflow-y: scroll;
  background: #fff;
}

.slidup {
  display: block;
  animation: slidup 0.2s ease-in both;
}

.disappear {
  display: none;
}

.show {
  display: block;
}

.container_hd {
  width: 100%;
  position: absolute;
  overflow-y: scroll;
  background-color: rgba(0, 0, 0, 0.5);
}

.nav-child.active .nav-title {
  color: #E72410;
}