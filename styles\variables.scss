// 全局 SCSS 变量定义
// Global SCSS Variables

// ==================== 颜色系统 Color System ====================
// 主色调
$primary-color: #E72410;           // 项目主色（红色）
$primary-light: #FF4D33;           // 主色浅色
$primary-dark: #CC1F0A;            // 主色深色

// 辅助色
$secondary-color: #1989fa;         // 辅助色（蓝色）
$success-color: #07c160;           // 成功色（绿色）
$warning-color: #ff976a;           // 警告色（橙色）
$danger-color: #ee0a24;            // 危险色（红色）
$info-color: #1989fa;              // 信息色（蓝色）

// 中性色
$white: #ffffff;
$black: #000000;
$gray-1: #f7f8fa;                  // 最浅灰
$gray-2: #f2f3f5;                  // 浅灰
$gray-3: #ebedf0;                  // 中浅灰
$gray-4: #dcdee0;                  // 中灰
$gray-5: #c8c9cc;                  // 深中灰
$gray-6: #969799;                  // 深灰
$gray-7: #646566;                  // 最深灰
$gray-8: #323233;                  // 接近黑色

// 文本颜色
$text-color-primary: #323233;      // 主要文本
$text-color-secondary: #646566;    // 次要文本
$text-color-disabled: #c8c9cc;     // 禁用文本
$text-color-link: #1989fa;         // 链接文本

// 背景色
$bg-color: #f7f8fa;                // 页面背景
$bg-color-light: #fafafa;          // 浅色背景
$bg-color-dark: #f2f3f5;           // 深色背景

// ==================== 尺寸系统 Size System ====================
// 间距
$spacing-xs: 8rpx;                 // 超小间距
$spacing-sm: 12rpx;                // 小间距
$spacing-md: 16rpx;                // 中等间距
$spacing-lg: 20rpx;                // 大间距
$spacing-xl: 24rpx;                // 超大间距
$spacing-2xl: 32rpx;               // 2倍超大间距
$spacing-3xl: 40rpx;               // 3倍超大间距
$spacing-4xl: 48rpx;               // 4倍超大间距

// 圆角
$border-radius-xs: 4rpx;           // 超小圆角
$border-radius-sm: 8rpx;           // 小圆角
$border-radius-md: 12rpx;          // 中等圆角
$border-radius-lg: 16rpx;          // 大圆角
$border-radius-xl: 20rpx;          // 超大圆角
$border-radius-2xl: 24rpx;         // 2倍超大圆角
$border-radius-round: 50%;         // 圆形

// 边框
$border-width: 1rpx;               // 标准边框宽度
$border-width-thick: 2rpx;         // 粗边框宽度
$border-color: #ebedf0;            // 边框颜色
$border-color-light: #f2f3f5;      // 浅色边框

// ==================== 字体系统 Typography ====================
// 字体大小
$font-size-xs: 20rpx;              // 超小字体
$font-size-sm: 24rpx;              // 小字体
$font-size-md: 28rpx;              // 中等字体
$font-size-lg: 32rpx;              // 大字体
$font-size-xl: 36rpx;              // 超大字体
$font-size-2xl: 40rpx;             // 2倍超大字体
$font-size-3xl: 48rpx;             // 3倍超大字体

// 字体粗细
$font-weight-light: 300;           // 细体
$font-weight-normal: 400;          // 正常
$font-weight-medium: 500;          // 中等
$font-weight-semibold: 600;        // 半粗体
$font-weight-bold: 700;            // 粗体

// 行高
$line-height-xs: 1.2;              // 紧凑行高
$line-height-sm: 1.4;              // 小行高
$line-height-md: 1.5;              // 中等行高
$line-height-lg: 1.6;              // 大行高
$line-height-xl: 1.8;              // 超大行高

// 字体族
$font-family: 'PingFang SC', 'PingFang SC-Semibold', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, sans-serif;

// ==================== 阴影系统 Shadow System ====================
$shadow-1: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);        // 轻微阴影
$shadow-2: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);        // 中等阴影
$shadow-3: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);       // 较重阴影
$shadow-4: 0 16rpx 32rpx rgba(0, 0, 0, 0.1);      // 重阴影

// ==================== 动画系统 Animation System ====================
// 动画时长
$duration-fast: 0.2s;              // 快速动画
$duration-base: 0.3s;              // 基础动画
$duration-slow: 0.5s;              // 慢速动画

// 动画曲线
$ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);

// ==================== Z-index 层级 ====================
$z-index-dropdown: 10;
$z-index-sticky: 50;
$z-index-fixed: 100;
$z-index-modal-backdrop: 1000;
$z-index-modal: 1010;
$z-index-popover: 1020;
$z-index-tooltip: 1030;
$z-index-toast: 1040;

// ==================== 渐变色 Gradients ====================
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-danger: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
$gradient-red: linear-gradient(135deg, #E72410 0%, #FF4D33 100%);

// ==================== 断点系统 Breakpoints ====================
$breakpoint-sm: 576px;             // 小屏幕
$breakpoint-md: 768px;             // 中等屏幕
$breakpoint-lg: 992px;             // 大屏幕
$breakpoint-xl: 1200px;            // 超大屏幕
