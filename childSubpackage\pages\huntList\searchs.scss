@import '../../../template/more/more.scss';
@import '../../../template/null/null.scss';
@import '../../../template/loading/index.scss';


/* input */
.pages {
  height: 100vh;
  overflow: hidden;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 缺省页 */
.queshen {
  width: 100%;
}

.tip {
  display: flex;
  align-items: center;
  width: 100%;
  background: #F7F7F7;
  height: 96rpx;
  padding-left: 32rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #74798C;
}

.tip text {
  font-weight: 600;
  color: #E72410;
  padding: 0 8rpx;
}

/* 卡片  */
.search-card {
  margin-bottom: 20rpx;
  margin-top: 0 !important;
}


.card-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 100rpx;
}

.card-box-text {
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  background: #fff;
  border: 1px;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
  border-top: 1px dashed #eeeeee;
}

.his_content1_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  height: auto;
}

.his_content1_item-l {
  flex: 1;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798C;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.his_content1_item-r {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #9B9EAC;
  min-width: 80rpx;
  flex-shrink: 0;
}

/* dialog文字样式 */
.dialog-con {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.dialog-con .map {
  position: relative;
  width: 100%;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #E72410;
}

.weui-dialog__title {
  font-weight: 500 !important;
  color: #000000 !important;
  line-height: 40rpx;
  font-size: 34rpx !important;
}

.light_bd {
  padding: 0 !important;
}

.dialog-con .map::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

/* 联系方式弹窗 */
.contact_box {
  position: relative;
  width: 100%;
  height: 96rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
}

.contact_box::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  bottom: 0;
  /* left: -50%; */
  transform: scaleY(0.5);
}

.contact_box:last-child::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: transparent;
  position: absolute;
  bottom: 0;
  /* left: -50%; */
  transform: scaleY(0.5);
}

.contact_left {
  display: flex;
  align-items: end;
  padding: 28rpx 0;
}

.contact_left image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.contact_number {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #E72410;
}

.contact_right {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

/* 开通vip */
.vip {
  margin-top: -324rpx;
}