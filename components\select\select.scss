/* components/select/select.scss */

.select-wrapper {
  position: relative;
  height: 100%;
  font-size: 28rpx;
}



.select-item-box {
  display: flex;
  height: 100%;
}

.select-item-box .select-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-item-box .select-item .icon {
  width: 20rpx;
  height: 20rpx;
  margin-left: 20rpx;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTKCkuqSks6CluaGluqCluqKnvaCmvKCluaCluqKlu6GluqCkuqCluhKbxYUAAAANdFJOUwDyDt1ApyEruZFHb2uSJ7UqAAAAT0lEQVQI12NgoBiw3oWCAGQmky2EdVmBgSERwhQDKmYBsy4uAGk8C2LeBpvBCWJOgJjXe/fuDQUIU/nuXSOoLWyyFxNgNvpKwS1nccDtMAAFpDC83MyRJwAAAABJRU5ErkJggg==") no-repeat center/100%;
}

.select-item-box .select-item.active {
  color: #E72410;
}

.select-item-box .select-item.active .icon {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAPhJREFUOE/t07sOAUEUBuD/UFgFq3PJFmR5BhFK2/EceCI8B50tiXgHG4qNS2cpELFHFBLL7I5NVGLKyZlvZs4/Q/jyoC97+BFwU0qWcQEyy/1M1iLplbd6onqliAkGonDraeswCUIDQbsY1yKITQnQ7ggDtotzRZsfbT/UF1zkoSjR1AiE2vNiZozP151RWOIkQn3Bta52QdQWnoS5l7WczsfgVldbLlEvqFfM3M5ZTv+15u2EjxAIpEjAkygkD/gaguyJiELygCtdHRFRXQZ5Q2IzZznGY84DroupAYBGGBDAMDvfNYVgSEhYLv0pYTf5g2E79l5/A9Q8URVSONEuAAAAAElFTkSuQmCC") no-repeat center/100%;
  transform: translateY(2rpx);
}


.select-wrapper .mask-box {
  width: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 99;
}

.select-wrapper .mask-box.hide {
  display: none;
}

.select-wrapper .mask-box.show {
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
}

.mask-box .select-dropdown {
  background-color: #fff;
  padding-bottom: 20rpx;
  border-radius: 0 0 20rpx 20rpx;
}

.select-dropdown .btn-box {
  height: 100rpx;
  padding: 20rpx 24rpx 0;
  display: flex;
  justify-content: space-between;
}

.select-dropdown .btn-box .btn {
  width: 340rpx;
  height: 80rpx;
  font-size: 34rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.select-dropdown .btn-box .btn.default {
  background-color: #f7f7f7;
  color: #74798C;
}

.select-dropdown .btn-box .btn.primary {
  background-color: #E72410;
  color: #fff;
}

.select-dropdown .select-scroll-dropdown-box {
  width: 100%;
  padding: 0 30rpx;
  max-height: 580rpx;
  border-top: 2rpx solid #f5f5f5;
  overflow-y: auto;
}

.mask-box .select-dropdown .select-dropdown-menu-item {
  height: 96rpx;
  padding: 0 20rpx;
  border-top: 2rpx solid #f5f5f5;
  color: #74798C;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mask-box .select-dropdown .select-dropdown-menu-item:first-of-type {
  border-top: none !important;
}


.select-dropdown-menu-item.active .text {
  color: #E72410;
  font-weight: 600;
}

.select-dropdown-menu-item.active .radio {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAR5JREFUWEftlj0OgkAQRmekoVHslM4ohdcwFhY23kcTLbT0LrZWXsArkFD506mVzTpmNSQkrDALu1oINeS9+WD2A+HHF/6YD5VAlcD/JHDo1PvoOGM/vKyTm/eVBN7w2g4Q20g0a4fXVSxhXSAJJ6K7I2jSim7brwjkwaWEtQQ4cGsCXLgVAR24cQFduFLg1PWG9MDQjy6RTlMWgacEzkFjJAA3SHgCAQOuRFF4SuDQ86aIuHxNThBxJMrAla/gGDTnALDgSJSFf/wIORIm4JlbkCVhCp67hioJAuHGxaI623U2J1dA3pCSAHJlq5mAswRSEnJBFK2mO7l2G8ZJmISzE4ht5TnhCNon+7zo5NoJlAV9et7a/wBXuBKoEngCQAv7IS1L+uMAAAAASUVORK5CYII=") no-repeat center/100%;
}

.select-dropdown-menu-item .check-box {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #dedede;
  border-radius: 2rpx;
}

.select-dropdown-menu-item.active .check-box {
  position: relative;
  border: 2rpx solid #E72410;
  background-color: #E72410;
}

.select-dropdown-menu-item.active .check-box::before {
  position: absolute;
  width: 4rpx;
  height: 14rpx;
  display: block;
  left: 18rpx;
  top: 6rpx;
  content: '';
  transform: rotate(50deg);
  background-color: #fff;
}

.select-dropdown-menu-item.active .check-box::after {
  position: absolute;
  width: 4rpx;
  height: 12rpx;
  display: block;
  left: 8rpx;
  top: 10rpx;
  content: '';
  transform: rotate(-50deg);
  background-color: #fff;
}

/* 选择按钮样式 */
.checkbtn-box {
  position: relative;
  width: 100%;
  background-color: #fff;
  padding-bottom: 168rpx;
  border-top: 1px solid #f7f7ff;
}

.scrollview {
  display: flex;
}

.content {
  padding: 0 32rpx;
  background-color: #fff;
}


.content .content-item {
  font-size: 25rpx;
  color: #5C6070;
  padding: 40rpx 0 20rpx;
  background-color: #fff;
}

.content-item .tit {
  font-size: 29rpx;
  font-weight: 700;
  color: #20263a;
}

.content-item .wrap {
  display: flex;
  flex-wrap: wrap;

}

.content-item .wrap>text {
  padding: 0 19rpx;
  height: 52rpx;
  line-height: 52rpx;
  background-color: #F4F4F4;
  /* border: 2rpx solid #F4F4F4; */
  margin-right: 19rpx;
  margin-top: 25rpx;
  border-radius: 4rpx;
  font-weight: 400;
}

.content-item .wrap text.active {
  /* border: 2rpx solid #E72410; */
  color: #E72410;
  background: rgba(7, 110, 228, 0.16);
}

.content-item .wrap .input-wrap {
  display: flex;
  align-items: center;
  margin-top: 25rpx;
}

.content-item .wrap .input-wrap>view {
  display: flex;
  width: 350rpx;
  height: 56rpx;
  padding: 0 19rpx;
  background-color: #F4F4F4;
  border-radius: 4rpx;
  margin-right: 19rpx;
  color: #9B9EAC;
}

.content-item .wrap .input-wrap>view .year {
  flex-shrink: 0;
  line-height: 56rpx;
}

.content-item .wrap .input-wrap>view input {
  height: 100%;
}

.content-item .wrap .input-wrap>view .short-line {
  width: 200rpx;
  height: 100%;
  line-height: 52rpx;
  text-align: center;
}

.content-item .wrap .input-wrap>view.active {
  /* border: 2rpx solid #E72410; */
  color: #E72410 !important;
  background-color: rgba(7, 110, 228, 0.10);
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.scrollbtn {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  height: 168rpx;
  /* 后面蒙层等级一定要高于这个 */
  z-index: 10;
  padding-top: 10rpx;
  background: #fff;
  border-top: 1px solid #f7f7f7;
}

.scrollbtn .btn1 {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(74deg, #EEEEEE 0%, #F5F5F5 100%);
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: CENTER;
  color: #74798c;
}

.scrollbtn .btn2 {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(90deg, #FFB2AA 0%,#E72410 100%);
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: CENTER;
  color: #ffffff;
  margin-left: 22rpx;
}

/* 地区 */
.district {
  width: 100%;
  height: auto;
  background: #fff;
}


/* 外面传进来的样式 改里面的值 */
.notes .select-item {
  color: #74798C !important;
}

.notes .select-item.active {
  color: #E72410 !important;
}