/* 框架 */
.region-wrap {
  width: 100%;
  min-height: 520rpx;
  display: flex;
  color: #20263a;
  font-size: 27rpx;
  /* border-top: 1px solid #eeeeee; */
  /* border-bottom: 1px solid #eeeeee; */
  position: relative;
}

.region-wrap::before {
  content: " ";
  width: 100%;
  height: 2rpx;
  background: #EEEEEE;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}

.province-wrap {
  height: 600rpx;
  width: 200rpx;
  background: #f7f7f7;
  /* border: 1px solid pink; */
}

.city-wrap,
.area-wrap {
  width: 280rpx;
  height: 600rpx;
  /* border-right: 1px solid #eeeeee; */
  position: relative;
}

.area-wrap::before {
  content: " ";
  height: 200%;
  width: 1px;
  background: #EEEEEE;
  position: absolute;
  top: -50%;
  transform: scaleX(0.5);
}

/* 每个item */

.region-wrap .list .item {
  width: 100%;
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  /* padding: 20rpx 92rpx 20rpx 104rpx; */
  padding: 24rpx 52rpx 24rpx 20rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: left;
  color: #20263a;
}

.region-wrap .list .items {
  padding: 24rpx 52rpx 24rpx 20rpx;
  text-align: left;
}

.region-wrap .actived {
  /* background: #e7f1fd; */
  font-weight: 600;
  color: #E72410 !important;
}