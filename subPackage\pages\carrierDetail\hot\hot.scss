/* subPackage/pages/carrierDetail/hot/hot.scss */

.hot-list-wrapper {
  width: 100%;
  background: #fff;
  position: relative;
}

.hot-list-wrapper .img-box {
  width: 100%;
  height: 444rpx;
  position: relative;
  margin-bottom: 20rpx;
}

.hot-list-wrapper .img-box:last-of-type {
  margin-bottom: 0;
}

.hot-list-wrapper .img-box image {
  width: 100%;
  height: 384rpx;
  pointer-events: all;
}

.hot-list-wrapper .img-box .fuzzy {
  width: 100%;
  padding: 10rpx 20rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 0px;
  box-sizing: border-box;
  position: absolute;
  top: 328rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFF;
  backdrop-filter: blur(28rpx);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.hot-list-wrapper .img-box .title {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  background: linear-gradient(360deg, #EDEDED 0%, rgba(237, 237, 237, 0.2) 100%);
  padding: 12rpx 20rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.hot-list-wrapper .img-box .title image {
  width: 28rpx;
  height: 28rpx;
  min-width: 28rpx;
  min-height: 28rpx;
}

.hot-list-wrapper .img-box .title text {
  line-height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  color: #3D4255;
  margin-left: 4rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}