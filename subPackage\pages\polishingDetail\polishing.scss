::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.artics {
  position: relative;
  -webkit-user-select: none;
  user-select: none;
  width: 100vw;
  height: 100vh;
  scroll-behavior: smooth;
  overflow-y: scroll;
  background: #f2f2f2;
}

.arti_head {
  background: #fff;
  padding-left: 32rpx;
  padding-right: 32rpx;
  padding-bottom: 28rpx;
}


.artic_head_t {
  font-size: 36rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 56rpx;
  text-align: left;
  padding-bottom: 28rpx;
  padding-top: 40rpx;
}

.artic_head_c {
  /* border: 1px solid red; */
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.artic-subt {
  display: flex;
  margin-bottom: 12rpx;
}

.artic-subt .one {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.artic-subt .two {
  flex-shrink: 0;
  margin-left: 40rpx;
}


.tags {
  display: flex;
  flex-wrap: wrap;
}

.tags view {
  display: flex;
  height: 38rpx;
  align-items: center;
  padding-left: 10rpx;
  padding-right: 10rpx;
  margin-right: 22rpx;
  padding-top: 2rpx;
  padding-bottom: 2rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

.tag0 {
  background: rgba(74, 184, 255, 0.1);
  color: #4AB8FF;
}

.tag1 {
  background: rgba(255, 185, 62, 0.15);
  color: #FFB93E;
}

.tag2 {
  background: rgba(38, 200, 167, 0.1);
  color: #26C8A7;
}

/* 报告下载 */
.artic_about {
  background: #FFF;
  margin-top: 20rpx;

}

.artic_about .title {
  height: 100rpx;
  display: flex;
  padding-left: 32rpx;
  align-items: center;
  border-bottom: 1rpx solid #EEEEEE;
}

.dwonCont {
  width: 100%;
  height: 526rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dwonCont-l {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 308rpx;
  height: 430rpx;
  background: #FFFFFF;
  box-shadow: 0px 0px 16rpx 0px rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
}

.dwonCont-one {
  padding: 8rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.dwonCont-one .img1 {
  width: 100%;
  height: 286rpx;
}

.dwonCont-one .bq {
  position: absolute;
  /* top: 16rpx; */
  top: 24rpx;
  /* left: -12rpx; */
  left: 0;
  display: flex;
  width: 152rpx;
  height: 56rpx;
  /* border: 1px solid red; */

}


.dwonCont-one .bq image {
  position: absolute;
  width: 100%;
  height: 100%;
}

.dwonCont-two {
  width: 100%;
  padding-left: 22rpx;
  padding-right: 22rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #3D4255;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}




.dwonCont-r {
  display: flex;
  margin-left: 68rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #4AB8FF 0%, #E72410 100%);
  border-radius: 4rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  justify-content: center;
  align-items: center;
}

.dwonCont-r .imgd {
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
}







/* 同类报告推荐 */

.cardCont {
  background: #fff;
  padding-bottom: 26rpx;
  padding-left: 26rpx;
  padding-right: 32rpx;
}

.icard {
  position: relative;
  padding: 40rpx 0rpx;
  display: flex;
  height: 224rpx;
  border-bottom: 1rpx solid #eee;
}

.dwonCont-card {
  position: relative;
  /* 164 */
  width: 144rpx;
  height: 144rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.dwonCont-card .imgBg {
  width: 100%;
  height: 100%;
  padding-left: 6rpx;
}

.dwonCont-card .img {
  position: absolute;
  left: 0;
  top: 16rpx;
  width: 92rpx;
  height: 34rpx;
}





.icard .imgs {
  height: 100%;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-right: 20rpx;
}

.imgs .img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  box-shadow: 0px 0px 8rpx 0px rgba(32, 38, 58, 0.1);
  overflow: hidden;
}

.icard-r {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.icard-r-tit {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 600;
  color: #20263A;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.icard-r-t {
  display: flex;
  flex-direction: column;
}

.p-tags {
  display: flex;
  /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  width: 513rpx; */
  width: 100%;
}

.p-tags view {
  display: inline-flex;
  align-items: center;
  height: 34rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  padding-left: 10rpx;
  padding-right: 10rpx;
  margin-right: 16rpx;
  border-radius: 4rpx;
  overflow: hidden;
}

.pbtm {
  display: flex;
  margin-top: 24rpx;
  justify-content: flex-start;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  width: 450rpx;
}

.pbtm .one {
  flex: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.pbtm .two {
  flex-shrink: 0;
  margin-right: 50rpx;
}

.po1 {
  background: rgba(74, 184, 255, 0.1);
  color: #4AB8FF !important;
}

.po2 {
  background: rgba(255, 185, 62, 0.15);
  color: #FFB93E !important;
}

.po3 {
  background: rgba(38, 200, 167, 0.1);
  color: #26C8A7 !important;
}

.po3 {
  background: rgba(38, 200, 167, 0.1);
  color: #26C8A7 !important;
}

.po4 {
  background: rgba(203, 155, 155, 0.10);
  color: #CB9B9B;
}