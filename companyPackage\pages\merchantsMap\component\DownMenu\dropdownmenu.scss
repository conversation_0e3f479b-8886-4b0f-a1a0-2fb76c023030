@import '../../../../../template/menuhead/index.scss';
@import '../../../../../components/hunt/index.scss';

.head {
  position: relative;
  z-index: 100;
}

.position {
  position: absolute;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.nav {
  height: 96rpx;
  position: relative;
  z-index: 11 !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* border-bottom: 1px solid #f7f7f7; */
  background: #fff;
  padding: 0 48rpx;
}

.no_border {
  border: none !important;
}

/* 导航总共高度为 48+40 rpx */
.nav-child {
  display: flex;
  text-align: center;
  align-items: center;
  /* justify-content: center; */
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.borders-right {
  border-right: 1px solid #e6e6e6;
}

.borders-left {
  border-left: 1px solid #e6e6e6;
}

.borders {
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
}

.nav-title {
  display: inline-block;
}

.icon {
  width: 20rpx;
  height: 20rpx;
  margin-left: 12rpx;
}

.container {
  position: relative;
  z-index: 4;
  font-size: 14px;
}

.slidedown {
  transform: translateY(0%);
}

@keyframes slidown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0%);
    opacity: 1;
  }
}

.slidown {
  display: block;
  animation: slidown 0.2s ease-in both;
}

@keyframes slidup {
  from {
    transform: translateY(0%);
    opacity: 1;
  }

  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

/* .z-height {
  overflow-y: auto;
  background: #fff;
} */

.slidup {
  display: block;
  animation: slidup 0.2s ease-in both;
}

.disappear {
  display: none;
}

.show {
  display: block;
}

.container_hd {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.nav-child.active .nav-title {
  color: #e72410;
}

.change_txt {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
  padding: 20rpx 0 0 32rpx;
  background-color: #fff;
}

.tier {
  position: fixed;
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  padding-right: 32rpx;
}

.tier view:nth-child(1) {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.tier view:nth-child(2) {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.tier .slider {
  position: relative;
  flex: 1;
}

.source-slider {
  opacity: 0;
}

.slider-item {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 默认slider  margin: 10px 18px; */
  padding-left: 18px;
  width: calc(100% - 20px);
  z-index: -1;
}

.slider-line {
  position: relative;
  width: 100%;
  height: 8rpx;
  background: #f2f2f2;
  border-radius: 2rpx;
}

.slider-line-b {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  /* padding-left: 12rpx; */
  /* right: -12rpx; */
  right: 0;
}

.slider-active {
  position: relative;
  height: 8rpx;
  background: #e72410;
  border-radius: 2rpx 0rpx 0rpx 2rpx;
}

.slider-active-block {
  position: absolute;
  right: 0;
  bottom: 50%;
  transform: translate(50%, 50%);
}

.circle {
  width: 28rpx;
  height: 28rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAx9JREFUSEu9ll9IU3EUx7/n7urU0rtQyj8E063UwEgwCIskJOwhlKIEqbCSyIceeggq8C3BoKBegiKypEIQqaQeCvqDGRgoFRnNzDkh3JQpboo62+49sRvT1N27q1l7veecz/mz3zlfQozfG0DMz5FKFIH2g1EE4jwCpaluzMMgDILxjhjP0wf8r2LFIy2DMCjPZjkC4jqA7LEC/U4Agwy+PCv7m7IHEYjmExXozU7ODQnCAxAVGQItNmJ8VBS5Kss1+W3xpyXAEWtKmSIKrQDWrggWcWLMsKwcyBycePFnnAXAMEw20RMiSvgrmA50DhhuY1AQPq0abD7jSUWWt0faqwJdViSYRUsnAdtWpbIlM+WuXqe/eA8QUoFum3SaiG7+E9h8e49nOH1NKtBjs7hAsOoC481Yc+wUEsorIdo2ASYRIUcPZtpaMN18F/g5GyNf7u/t9+fTcI5UygK91LM2ZW3EusZWiDmbo5qFnH0YrzkEeeiHXhgmhfeSxyZdAdE5Tct4M9KevtWERfzC0NHy3bqVMnCdPHapE6AdWsCk6lqk1DUYGu9kQx2mGm9o2zI6whV6QJSuZZX66DXiCgoNAYNfP2OsokQHyF7y2C2sF22DwwsSRUNADgQwUpChP8eYwC8ekNnY4uFQECP56/WBbrvknTs3UUxT29oRt2WroQqDPR8wdrBUb4YectssHUTYpfmnqTmDlAuXDAEn6i9iuklvf/B7GrZbrjFw9n88CzBfNf7w77RCtGk8/IE+jJ80+PDVy26XHDGverwZSVUnkFhRCTG/AJBDCA18R6CtBVP3bxtYbXBl9PtyIru0GoR7hga1QiNmrs10+m+pwHCVuTapk1YqKWIlwfgYkH3FYZ0zd4CHspNzBZOpC0ByLP9lfWfMKIpcuOAARwK4rSllZBIeg5C4rKBaxlF0zRIRtYrQSQ4ph3VFVCRRtb2CqRkEY1t7UYXM3M2KctSQTIz4qjrHJFUT0XlAVQOaonmex/1gqu91+h6G9Uu0ThsIAqiqgLCPCDuZyQqCehIYPAqmXoHQDYWfOQb87VqgCPwX8UlC5j37kUoAAAAASUVORK5CYII=')
    no-repeat;
  background-size: 100% 100%;
}

.scroll_h {
  height: calc(100vh - 230rpx);
}

/* 筛选相关 */
/* companyPackage/pages/companyListFilter/companyListFilter.scss */

.container .footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  height: 85px;
  background-color: #fff;
  border-top: 2rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  padding: 10rpx 31rpx 0;
}

.container .footer text {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(90deg, #ffb2aa 0%, #e72410 100%);
  border-radius: 8rpx;
  color: #fff;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
}

.container .footer .reset {
  background: linear-gradient(74deg, #eeeeee 0%, #f5f5f5 100%);
  font-size: 34rpx;
  text-align: CENTER;
  color: #74798c;
}

.merch {
  width: 702rpx;
  height: 72rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  margin: 28rpx 24rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  z-index: 11;
}

.merch image {
  width: 40rpx;
  height: 40rpx;
  margin-left: 28rpx;
  margin-right: 20rpx;
}

.merch-input {
  width: 80%;
  font-size: 28rpx;
  color: #9b9eac;
}

.placeholder_class {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}
.head_nav {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  transition: all 0.3s ease;
}

/* 下拉框区域样式 */
.dropdown-section {
  flex: 1;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateX(0);
}

.dropdown-section.hidden {
  opacity: 0;
  transform: translateX(-20rpx);
  pointer-events: none;
}

/* 搜索框区域样式 */
.search-section {
  transition: all 0.3s ease;
}

.search-section.collapsed {
  width: 140rpx;
  flex-shrink: 0;
  height: 64rpx;
  background: #f4f4f4;
  border-radius: 8rpx;
  overflow: hidden;
}

.search-section.expanded {
  width: 100%;
  height: 64rpx;
  overflow: hidden;
  margin: 12rpx 0;
}

/* 搜索容器样式 */
.search-container {
  height: 64rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.search-container .left {
  display: flex;
  align-items: center;
  flex: 1;
  background: #f4f4f4;
  border-radius: 8rpx;
  height: 100%;
}
.left .cancel {
  position: relative;
  font-weight: 400;
  font-size: 28rpx;
  color: #20263a;
  width: 96rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.left .cancel::after {
  content: '';
  position: absolute;
  width: 2rpx;
  height: 32rpx;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  background: #dedede;
}
.cancel.show {
  opacity: 1;
}
.cancel.hide {
  opacity: 0;
  width: 0;
}

/* 搜索图标样式 */
.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-left: 14rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

/* 搜索输入框样式 */
.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #20263a;
  background: transparent;
  border: none;
  outline: none;
  min-width: 0;
}

.search-input.placeholder_class {
  color: #9b9eac;
}

/* 收起按钮样式 */
.collapse-btn {
  font-weight: 400;
  font-size: 28rpx;
  color: #74798c;
  background: transparent;
  border: none;
  padding: 0 0 0 24rpx;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.collapse-btn.hide {
  opacity: 0;
  pointer-events: none;
  width: 0;
}

.collapse-btn.show {
  opacity: 1;
  pointer-events: auto;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .search-section.collapsed {
    width: 120rpx;
  }

  .search-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 20rpx;
    margin-right: 16rpx;
  }

  .collapse-btn {
    right: 16rpx;
    padding: 0 12rpx;
    font-size: 26rpx;
  }
}
