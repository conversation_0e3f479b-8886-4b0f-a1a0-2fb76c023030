.layout-box {
  background-color: #fff;
  border-radius:8rpx;
}

.layout-box .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 92rpx;
  padding: 24rpx;
}

.layout-box .title .left {
  font-size: 32rpx;
  font-family: <PERSON>Fang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  flex: 1;
}
.right1 {
  width: 20rpx;
  height: 20rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAhklEQVQoU2NkIBIwEqmOAUPh/CW7MhgY/jEkxnjMQDYERSFIESMjQx9Iwf///4qQFcMVwhQxMTAGghT+Y/i/HlkxWCGyorho150gsUVLd7sjK2act2SPDxPjv1Ugk2CKYG6DK2b8H864bds29jdvmA3i4txPYguBRYt2mouI/L1AfvDgClcAzzU+fyVW8ZcAAAAASUVORK5CYII=') no-repeat;
  background-size: 100%;
  background-position: center;
}
.right2 {
  width: 20rpx;
  height: 20rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAhUlEQVQoU2NkIBIwEqmOgXH+/PkczBxS+nGR7iexaVq0fKf53x/PLjLOX7bTl/E/40omBsbAuGjXnciKFy3d7f6P4f/6f///h4Gtnr9kVwYjI0MfsmKYov//GYoSY9xmwN2IrBikGWQSTBGIj+IZmGKQBLIiDIUwZ4BokHXI7iU+eIgNRwAavDs3QR00bgAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100%;
  background-position: center;
}
.layout-box .content {
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1rpx solid #EEEEEE;

}

.layout-box .content .box {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  background-color: #076EE4;
}
.layout-box .content .box .title {
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-top: 56rpx;
  margin-bottom: 40rpx;
  color: #20263A;
}
