/**
 * 状态管理器
 * 优化组件状态管理，提供响应式状态更新和缓存机制
 */

const { DEFAULT_PARAMS } = require('./constants.js');

/**
 * 状态观察者
 */
class StateObserver {
  constructor() {
    this.observers = new Map();
  }

  /**
   * 订阅状态变化
   * @param {string} key - 状态键
   * @param {Function} callback - 回调函数
   */
  subscribe(key, callback) {
    if (!this.observers.has(key)) {
      this.observers.set(key, []);
    }
    this.observers.get(key).push(callback);
  }

  /**
   * 取消订阅
   * @param {string} key - 状态键
   * @param {Function} callback - 回调函数
   */
  unsubscribe(key, callback) {
    if (this.observers.has(key)) {
      const callbacks = this.observers.get(key);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 通知观察者
   * @param {string} key - 状态键
   * @param {*} newValue - 新值
   * @param {*} oldValue - 旧值
   */
  notify(key, newValue, oldValue) {
    if (this.observers.has(key)) {
      this.observers.get(key).forEach(callback => {
        try {
          callback(newValue, oldValue, key);
        } catch (error) {
          console.error(`Observer error for key ${key}:`, error);
        }
      });
    }
  }

  /**
   * 清理所有观察者
   */
  clear() {
    this.observers.clear();
  }
}

/**
 * 状态缓存管理器
 */
class StateCacheManager {
  constructor(maxSize = 100) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessOrder = [];
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   */
  set(key, value) {
    // 如果已存在，更新访问顺序
    if (this.cache.has(key)) {
      this.updateAccessOrder(key);
    } else {
      // 检查缓存大小
      if (this.cache.size >= this.maxSize) {
        this.evictLeastRecentlyUsed();
      }
      this.accessOrder.push(key);
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      accessCount: 1
    });
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {*} 缓存值
   */
  get(key) {
    if (this.cache.has(key)) {
      const item = this.cache.get(key);
      item.accessCount++;
      this.updateAccessOrder(key);
      return item.value;
    }
    return null;
  }

  /**
   * 更新访问顺序
   * @param {string} key - 缓存键
   */
  updateAccessOrder(key) {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
      this.accessOrder.push(key);
    }
  }

  /**
   * 淘汰最近最少使用的缓存
   */
  evictLeastRecentlyUsed() {
    if (this.accessOrder.length > 0) {
      const lruKey = this.accessOrder.shift();
      this.cache.delete(lruKey);
    }
  }

  /**
   * 清理过期缓存
   * @param {number} maxAge - 最大存活时间（毫秒）
   */
  cleanExpired(maxAge = 5 * 60 * 1000) { // 默认5分钟
    const now = Date.now();
    const expiredKeys = [];

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > maxAge) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      const index = this.accessOrder.indexOf(key);
      if (index > -1) {
        this.accessOrder.splice(index, 1);
      }
    });
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate()
    };
  }

  /**
   * 计算命中率
   */
  calculateHitRate() {
    let totalAccess = 0;
    this.cache.forEach(item => {
      totalAccess += item.accessCount;
    });
    return totalAccess > 0 ? (this.cache.size / totalAccess) : 0;
  }
}

/**
 * 状态管理器主类
 */
class StateManager {
  constructor() {
    this.state = this.createReactiveState();
    this.observer = new StateObserver();
    this.cache = new StateCacheManager();
    this.history = []; // 状态历史记录
    this.maxHistorySize = 50;
    
    // 初始化状态
    this.initializeState();
    
    // 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cache.cleanExpired();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 创建响应式状态
   */
  createReactiveState() {
    const self = this;
    
    return new Proxy({}, {
      set(target, key, value) {
        const oldValue = target[key];
        
        // 检查值是否真的发生了变化
        if (JSON.stringify(oldValue) !== JSON.stringify(value)) {
          target[key] = value;
          
          // 记录状态历史
          self.recordHistory(key, value, oldValue);
          
          // 通知观察者
          self.observer.notify(key, value, oldValue);
          
          // 缓存计算结果
          self.cacheComputedValue(key, value);
        }
        
        return true;
      },
      
      get(target, key) {
        // 尝试从缓存获取计算值
        const cached = self.cache.get(`computed_${key}`);
        if (cached) {
          return cached;
        }
        
        return target[key];
      }
    });
  }

  /**
   * 初始化状态
   */
  initializeState() {
    // 设置默认参数
    this.state.params = JSON.parse(JSON.stringify(DEFAULT_PARAMS));
    
    // 设置UI状态
    this.state.ui = {
      loading: false,
      error: null,
      focus: false,
      activeTab: 0,
      expandedItems: new Set(),
      selectedItems: new Set()
    };
    
    // 设置输入状态
    this.state.inputs = {
      minCapital: '',
      maxCapital: '',
      minDate: '',
      maxDate: '',
      socialminPeson: '',
      socialmaxPeson: '',
      dateType: '',
      capitalActive: false,
      socialActive: false,
      dateActive: false
    };
    
    // 设置弹窗状态
    this.state.popups = {
      searPop: false,
      saveSearPop: false,
      regionPop: false,
      datePop: false,
      eleseicPop: false,
      enttypePop: false,
      districtPop: false,
      chainCodePop: false,
      vipVisible: false
    };
  }

  /**
   * 记录状态历史
   * @param {string} key - 状态键
   * @param {*} newValue - 新值
   * @param {*} oldValue - 旧值
   */
  recordHistory(key, newValue, oldValue) {
    this.history.push({
      key,
      newValue: JSON.parse(JSON.stringify(newValue)),
      oldValue: JSON.parse(JSON.stringify(oldValue)),
      timestamp: Date.now()
    });

    // 限制历史记录大小
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }

  /**
   * 缓存计算值
   * @param {string} key - 状态键
   * @param {*} value - 值
   */
  cacheComputedValue(key, value) {
    // 缓存一些计算密集的值
    if (key === 'params') {
      const processed = this.processParams(value);
      this.cache.set(`computed_${key}`, processed);
    }
  }

  /**
   * 处理参数（示例计算函数）
   * @param {Object} params - 参数对象
   * @returns {Object} 处理后的参数
   */
  processParams(params) {
    // 这里可以进行复杂的计算
    return {
      ...params,
      _processed: true,
      _timestamp: Date.now()
    };
  }

  /**
   * 获取状态
   * @param {string} path - 状态路径，支持点号分隔
   * @returns {*} 状态值
   */
  getState(path) {
    if (!path) return this.state;
    
    const keys = path.split('.');
    let current = this.state;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  /**
   * 设置状态
   * @param {string} path - 状态路径
   * @param {*} value - 状态值
   */
  setState(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = this.state;
    
    // 导航到目标对象
    for (const key of keys) {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    // 设置值
    current[lastKey] = value;
  }

  /**
   * 批量更新状态
   * @param {Object} updates - 更新对象
   */
  batchUpdate(updates) {
    Object.keys(updates).forEach(path => {
      this.setState(path, updates[path]);
    });
  }

  /**
   * 重置状态
   * @param {string} path - 状态路径，不传则重置所有
   */
  reset(path) {
    if (path) {
      // 重置特定路径
      if (path === 'params') {
        this.setState('params', JSON.parse(JSON.stringify(DEFAULT_PARAMS)));
      } else if (path.startsWith('inputs')) {
        const inputKey = path.split('.')[1];
        if (inputKey) {
          this.setState(path, '');
        }
      }
    } else {
      // 重置所有状态
      this.initializeState();
    }
  }

  /**
   * 订阅状态变化
   * @param {string} path - 状态路径
   * @param {Function} callback - 回调函数
   */
  subscribe(path, callback) {
    this.observer.subscribe(path, callback);
  }

  /**
   * 取消订阅
   * @param {string} path - 状态路径
   * @param {Function} callback - 回调函数
   */
  unsubscribe(path, callback) {
    this.observer.unsubscribe(path, callback);
  }

  /**
   * 撤销操作
   * @returns {boolean} 是否成功撤销
   */
  undo() {
    if (this.history.length > 0) {
      const lastChange = this.history.pop();
      this.setState(lastChange.key, lastChange.oldValue);
      return true;
    }
    return false;
  }

  /**
   * 获取状态快照
   * @returns {Object} 状态快照
   */
  getSnapshot() {
    return JSON.parse(JSON.stringify(this.state));
  }

  /**
   * 从快照恢复状态
   * @param {Object} snapshot - 状态快照
   */
  restoreFromSnapshot(snapshot) {
    Object.keys(snapshot).forEach(key => {
      this.state[key] = snapshot[key];
    });
  }

  /**
   * 销毁状态管理器
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.observer.clear();
    this.cache.clear();
    this.history = [];
  }
}

module.exports = {
  StateManager,
  StateObserver,
  StateCacheManager
};
