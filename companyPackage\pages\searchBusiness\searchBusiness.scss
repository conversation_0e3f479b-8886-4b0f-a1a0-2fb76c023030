/* companyPackage/pages/searchBusiness/searchBusiness.scss */
.search-business {
  background-color: #F7F7F7;
  min-height: calc(100vh - 28rpx);
}

/* input */
.search-wrapper {
  padding: 28rpx 24rpx;
  background-color: #fff;
}

.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #E72410;
  color: #74798c;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
  /* border-right: 1px solid hsla(229, 9%, 64%, 0.5); */
  /* border: 1px solid red; */
}

.s-input-item::after {
  content: "";
  height: 64rpx;
  width: 2px;
  background: #DEDEDE;
  /* background: red; */
  position: absolute;
  right: 0;
  /* top: 50%; */
  transform: scale(0.5);
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.search-cancel {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
  padding: 0 28rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  /* border: 1px solid red; */
}

/* 最近搜索和浏览历史样式  */

.search_b {
  padding-bottom: 0 !important;
}

.his_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
}
.more {
  font-size: 24rpx;
  color: #E72410;
  padding-right: 28rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAL9JREFUOE+d1DEKAjEQBdA/2S0s0wk2ZsGD6C32KHspT6AQ72GhldZ2FgsjKwgSMslMUubDg4HPJwgvAv0BmKVc+qdc8Aw+oOcTE6bN9XW0oHlw5yOAPcAzE0YLmgUfg9+SwwWEYEWz4HJiKyqCrWgRbEGroBVVgRZUDWpRE/gtfIf4q5MjjOuk+GpQgy1XqEAtpgItWBW0YkWwBRPBVkwG/+YrV43SPsrz1fHZEaa0Z7WxFWtzC1gNd7xrQJp/AFhlihWrnSPdAAAAAElFTkSuQmCC') right center no-repeat;
  background-size: 20rpx 20rpx;
}
.his_titles {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.his_title_l {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.his_title_icon {
  width: 40rpx;
  height: 40rpx;
}

.his_content {
  display: flex;
  width: 100%;
  overflow: hidden;
  /* border: 1px solid red; */
  /* height: 168rpx; */
  height: 160rpx;
  padding: 0 32rpx;
}

.text-box {
  text-align: justify;
  display: flex;
  flex-wrap: wrap;
  /* text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical; */
}

.his_content_item {
  /* display: inline-flex; */
  background: #f5f6f7;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  margin-right: 20rpx;
  padding: 8rpx 20rpx;
  min-width: 96rpx;
  max-width: 680rpx;
  height: 56rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  justify-content: center;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
}

/* 暂无历史搜索 */
.his_content_none {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 104rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.his_content1 {
  margin-top: 32rpx;
  padding: 0 32rpx;
}

.his_content1_item {
  height: 80rpx;
  font-size: 28rpx;
  display: flex;
  color: #74798C;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.his_content1_item-l {
  padding-right: 20rpx;
}
.his_content1_item-r {
  font-size: 24rpx;
  font-weight: 400;
  padding: 0 10rpx;
  height: 34rpx;
  line-height: 34rpx;
  flex-shrink: 0;
}
.his_content1_item-r.LISTED {
  border: 1rpx solid #FD9331;
  color: #FD9331;
}
.his_content1_item-r.CHAIN {
  border: 1rpx solid #26C8A7;
  color: #26C8A7;
}
.his_content1_item-r.ENTERPRISE {
  border: 1rpx solid #4AB8FF;
  color: #4AB8FF;
}
/* keywords */
.keywords-wrapper {
  /* padding: 0 32rpx; */
}

.s-title {
  line-height: 44rpx;
  font-size: 28rpx;
  color: #20263A;
  margin-left: 20rpx;
}
.keywords {
  padding-left: 12rpx;
}
.keyword-tag {
  display: inline-block;
  padding: 8rpx 20rpx;
  line-height: 40rpx;
  background: #F5F6F7;
  border-radius: 8rpx;
  color: rgba(116, 121, 140, 1);
  font-size: 28rpx;
  margin: 0 0 14rpx 20rpx;
  max-width: 50%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-high {
  color: #E72410;
}

.industrial-chain {
  position: relative;
}

.industrial-box {
  margin-top: -20rpx;
  padding: 0 32rpx;
}

.show-all {
  display: flex;
  padding: 20rpx 0;
  padding-bottom: 0;
  justify-content: center;
  align-items: center;
  line-height: 40rpx;
  font-size: 28rpx;
  color: #E72410;
}

.show-all .show-text {
  min-width: max-content;
}

.show-all image {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

.industrial-box .industrial-desc {
  padding: 20rpx 0;
  line-height: 40rpx;
  font-size: 28rpx;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mixin */
.item_box {
  background-color: #fff;
  padding: 32rpx;
  margin-top: 20rpx;
}

.no_data {
  color: #74798c;
  text-align: center;
  /* padding-top: 80rpx; */
}

.ml-0 {
  margin-left: 0;
}

.underline {
  border-top: 1px solid #eeeeee;
}

.mt-20 {
  margin-top: 20rpx;
}

.bg-fff {
  background: #fff;
}
.pundit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  padding: 0 32rpx 32rpx;
  color: #74798C;
}