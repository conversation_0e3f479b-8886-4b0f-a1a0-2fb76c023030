
@import '../../../../template/more/more.scss';
@import '../../../../template/null/null.scss';
@import '../../../../template/loading/index.scss';

.information_wrapper {
  height: 100vh;
  overflow: hidden;
  background-color: #F7F7F7;
}
.sci_scroll {
  background-color: #FFFFFF;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* input */
.searchs {
  height: 112rpx;
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFFFFF;

}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
  margin: 20rpx 24rpx;
  background: #F4F4F4;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 24rpx;

}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #E72410;
  color: #74798c;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
  /* border-right: 1px solid hsla(229, 9%, 64%, 0.5); */
  /* border: 1px solid red; */
}

.s-input-item::after {
  content: "";
  height: 64rpx;
  width: 2px;
  background: #DEDEDE;
  /* background: red; */
  position: absolute;
  right: 0;
  /* top: 50%; */
  transform: scale(0.5);
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.search-cancel {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
  padding: 0 28rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  /* border: 1px solid red; */
}


/* 筛选框下文字 */
.sci_text {
  height: 96rpx;
  line-height: 96rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  padding-left: 24rpx;
  width: 100%;
  background-color: #F7F7F7;
}

.sci_text>text {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #E72410;
}

.sci_line {
  width: 100%;
  height: 0rpx;
  /* border-bottom: 1px solid #f2f2f2; */
}


/* 列表 */
.information {
  height: 100%;
  width: 100%;
  background: #FFFFFF;
}

.information .list {
  width: 100%;
  height: 144rpx;
  padding: 32rpx 24rpx;
  display: flex;
  border-bottom: 1rpx solid #EEEEEE;
}

.list_box>view:last-child {
  border-bottom: none !important;
}

.information .list .info_img {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.information .list .info_title {
  flex: 1;
  height: 80rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.information .list .info_time {
  min-width: 140rpx;
  height: 34rpx;
  margin-left: 20rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}