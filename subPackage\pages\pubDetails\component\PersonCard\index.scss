 .card {
     position: relative;
     background: #ffffff;
     border-radius: 8rpx;
     box-shadow: 0rpx 4rpx 18rpx 0rpx rgba(221, 221, 221, 0.50);
     padding: 32rpx;
     margin-bottom: 20rpx;
 }

 .card-bg {
     position: absolute;
     width: 130rpx;
     height: 100rpx;
     right: 32rpx;
     bottom: 20rpx;
 }

 .card-h {
     display: flex;
     justify-content: space-between;
     align-items: center;
     padding-bottom: 8rpx;
 }

 .card-h .title {
     font-size: 30rpx;
     font-family: PingFang SC, PingFang SC-Semibold;
     font-weight: 600;
     color: #20263a;
 }

 .card-h .zhiwei {
     font-size: 24rpx;
     font-family: PingFang SC, PingFang SC-Regular;
     font-weight: 400;
     margin-left: 16rpx;
     padding: 2rpx 24rpx;
     text-align: center;
     background: rgba(253, 147, 49, 0.1);
     border-radius: 4rpx 4rpx 4rpx 4rpx;
     color: #FD9331;
 }

 .card-h .lan {
     font-size: 24rpx;
     font-family: PingFang SC, PingFang SC-Regular;
     font-weight: 400;
     margin-left: 16rpx;
     padding: 2rpx 24rpx;
     text-align: center;
     background: rgba(38, 200, 167, 0.1);
     border-radius: 4rpx 4rpx 4rpx 4rpx;
     color: #26C8A7;
 }

 .icon {
     display: flex;
     align-items: center;
 }

 .icon-l {
     width: 40rpx;
     height: 40rpx;
     margin-right: 20rpx;
     background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAO5JREFUWEftl10KwjAQhDeeQY9kXzxCEXK0gngEX/RI9gxWBAOl2MzWMRJx+pru7uTbnyTBKv9C5fpMAtkM0QS743nIiYhtQ8WgjB/CqhfIphDZ0wRRAHZ9ViBKHRt4aj9Xq78rMO0wkWS7cUrM6xfWoNfR0pR7/UrgHNmvEERBcuvINm2MSjEKIoHjs/bVmBFBM3xhZeqIsVWTsPUrgiLouW5pDmoOPt/M7xyT/zFmll7zx/+jBvsIQQlku7gWglczWzNiMrZ9bJtNzjd+kxwuO1sNXQGRvd1CjPvtiRJYiJzbLSTo9lToRwlkwd4BmHZdOOnEu04AAAAASUVORK5CYII=');
     background-size: 100% 100%;
 }

 .icon-r {
     width: 40rpx;
     height: 40rpx;
     background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAwZJREFUWEftlz9oE2EYxp/3mksLLdhuBZcsgkMWcRBUUFDTFDsIRq25tL3MVVRw0MlzcqnYod2ES+KlVVtxqKXmD6JY2qWjghDBCCI6SbCFQuO9kmsKSUyT+5KcVsi3hXzf9/7y3Ps+T46wxxftcT60AZt9Qm0F/xsFdX3JQ3KHzlu/wuHwYNYu+F95xLGZ5AnT5Mcg6gfzx41c7tD4+MV1O5COA0aN9CUmMwKgC0AegAvgdDazOqhpWuFzzeUoYDSeusNgzSJgnoTLNYV8ftlSEphSFd/VfwKo63oXde5/BEbAUo1xXQ35pgswESN5DEAahC4wbqoh3/1akC1XUNcX+8ktzwE4DmBdAgVGlTOJUojITOo8mOcteInOqZfPLO4G2VLAh7EXXleHvACQB+AsiIfUoP99teIRI7EEIn/hR8jY8irK2c/V9rUMMBZPDZiwVOkB47VbkoaDwdPfK4tqmubyHDj6AMCVwncE1saUgbuOKhiNJ68xMGFNKPM8b30dCYfDm5VFp6ef9nT37ZsDkx+MTUhmSA36nznWgyJqFI16AQQvmL9JEg2PBn1vHJtiETUqjPqdTK4hRTlVtecqgRvqQUsNt7QE0MF6ahQn1rCMmvnlRi53wW6KbPeo4BJRo8yogalsZuWGnfQoRRICLIutGmrUMmpBPewraFcNy6g75VkwTu5m1CKQdRUUUUPEqO1C1gS0E1s7hcqMGlh2kxSoZtR2wXb21QSMGol7THSrfmwlx0GYrGfUonB1pzhipBYKeQrTDKgjfzq+iFE3AlcfMJ78BMADMr2VoV9p1ARWx0IDTxoFEc5iC6C392dhErOZlb5S/2o0thqB37UHI/FXh4H8Gpi/SBKFdi5nk/sZmNz+V8wfZLj8dmOrpYBRIz1WfJeofm8DsdVSwNhs4ohp0gTYetEpX8Rr2czqbdHYailgI5c5caZukjhRVOTONqCIWtX2lilY9L63zV7azHk3Sf7SDC8D1PXnveTu/tFMgWbPyujwlPpquwebVbStYLMK/ga61Yg4zzlahAAAAABJRU5ErkJggg==');
     background-size: 100% 100%;
 }



 .card-list {
     display: flex;
     margin-top: 28rpx;
 }

 .card-list .img {
     width: 32rpx;
     height: 32rpx;
 }

 .card-list-k {
     font-size: 24rpx;
     font-family: PingFang SC, PingFang SC-Regular;
     font-weight: 400;
     color: #74798c;
     padding-left: 20rpx;
 }

 .card-list-v {
     font-size: 24rpx;
     font-family: PingFang SC, PingFang SC-Regular;
     font-weight: 400;
     text-align: LEFT;
     color: #20263a;
     /* padding-left: 8rpx; */
 }

 .blue {
     color: #E72410;
 }