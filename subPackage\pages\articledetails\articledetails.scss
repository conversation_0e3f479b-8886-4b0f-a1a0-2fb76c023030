@import '/subPackage/template/wxParse/wxParse.scss';

.artics {
  position: relative;
  -webkit-user-select: none;
  user-select: none;
  width: 100vw;
  overflow-x: hidden;
  height: 100%;
}

/* 文章 */
.artic_cont {
  padding: 40rpx 0;
  height: 100%;
  scroll-behavior: smooth;
  overflow-y: scroll;
}

.par {
  font-family: PingFang SC-Regular, PingFang SC;
  color: #20263A;
}

/* 富文本--文字单独处理 */
.par .wxParse-b .WxEmojiView {
  line-height: 80rpx;
}

.par .wxParse-p .WxEmojiView {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 48rpx;
}



.artic_head_t {
  font-size: 36rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 56rpx;
  text-align: left;
  padding-bottom: 28rpx;
}

.artic_head_c {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 40rpx;
}

.artic_head_c_l,
.artic_head_c_r {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #9b9eac;
}

.artic_head_c_l text {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #E72410;
}

/* 间隙 */
.space-1 {
  width: 10rpx;
}

.space-2 {
  width: 40rpx;
}

/* 是否富文本--这里不是 */
.artic {
  width: 100%;
  padding: 0rpx 32rpx 40rpx;
  height: auto;
}

.artic_cont_text {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a
}

.artic_cont_img {
  margin-top: 40rpx;

  width: 686rpx;
  height: 456rpx;
}

.artic_cont_ps {
  margin-top: 60rpx;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
  line-height: 40rpx;
}

.artic_cont_ps text {
  color: rgba(7, 110, 228, 1);
}

/* 相关企业 */
.artic_about {
  border-top: 20rpx solid #f2f2f2;
}

.artic_about .title {
  padding: 28rpx 24rpx;
  border-bottom: 1px solid #f2f2f2;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.artic_about .cont {
  padding: 40rpx 24rpx;
}

.artic_about .conts {
  padding: 0 24rpx;
}

.artic_about .cont .list {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #E72410;
  padding-bottom: 28rpx;
}

/* 相关情报 */
.artic_about_card {
  display: flex;
  align-items: center;
  padding: 40rpx 0;
  border-bottom: 1px solid #f2f2f2;
}

.artic_about_card_r {
  flex-shrink: 0;
  width: 144rpx;
  height: 144rpx;
  margin-left: 24rpx;
}

.artic_about_card_l {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  min-height: 144rpx;
}

.artic_about_card_l .titles {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.artic_about_card_l .textbtm {
  display: flex;
  align-items: center;
}

.textbtm .zhanwei {
  /* 遇到yyyy-MM-dd 28的宽度就会挤下去 */
  display: inline-block;
  /* max-width: 28rpx; */
  min-width: 14rpx;
}

.textbtm .texts {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}

.textbtm .texts text {
  color: rgba(7, 110, 228, 1);
}

/* foot */
.artic_foot {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  box-shadow: 8rpx 0rpx 8rpx 0rpx rgba(204, 204, 204, 0.20);
}

.artic_foot_wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  padding: 16rpx 32rpx 0;
}

.artic_foot_item {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: relative;
  /* border: 1px solid red; */
}

.artic_foot_item_b {
  font-size: 20rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
  padding-top: 4rpx;
}

.btn {
  position: absolute;
  left: 0;
  right: 0;
  padding: 0;
  margin: 0 !important;
  height: 100%;
  width: 54px !important;
  background: transparent;
  z-index: 10;
}

.artic_foot_about {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #E72410;
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: CENTER;
  color: #ffffff;
}


/* 图标 */
.zan {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTKGluaCluqCluqCluqCluqGhuKGlupycr6CluqGlu6Glu6GluqCluqCluqClujO7KToAAAAPdFJOUwBeoOH1yR92Da9HUC+QZ1eusQIAAADbSURBVCjPY2CgDji+A4tg/JcHGGJcX+K/Ywhyfuf7koAueN6DQb8AXfD+BQb5CWhibP4HGPajC7L/ZMAUbP6FRVA+AItgvABQcFpaWloGktM/NwAF/wPBFw0kpwOJxcZAYPIVItK1UbDeA+42+wVgrf5AXRcQvgD7i/WrkpK9AEIQHFj8PxgY9BGC8WDt/B9QBO0TMAU5vjBgCvJ8xSLI+BOLIPt3LIKcH7EIsv7BIsjyA4tgvgEWwfUBWATfK2ARhEY+C3KAcAGjGuyI/8jgL9S3IUhinwsY6AkAkzJowpj59cYAAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.wuxin {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA5UExURUdwTKCkuaCkuqGhtKKku6GluqKlupycpKCluaCluqGluqGluqGlu6GluqCluqCkuqCluqCluqClugNaeKIAAAASdFJOUwDmvxMfYD8J3tRTgi1vxPOasiI0u1MAAAEoSURBVDjLzZRbloQgDAUFQR4Cwt3/Yic8nFbE03w2Px5jUSExuiw/tfZ9jtuAbQrkAJ8TsmNKyeH3GSUJ1TqjJCHV/V2ZhcsyoSzCCWUVflUqZ6owK4NTI8L6yBkA3Z6umm4Yj95+eFmIvA4TxRkV0RwtzLgtoUCETlFuok8kNh+TwflKhUZQ7wdfA3RTEGnWV878c5UUY05cuW7bjeuTXQ7ScaE7lOID0o3KVAms36yRRm8nwN4j8qVtEbIH47ATph+YDWYIHugSrThGnIPuQxqj5lqkM+XW1Kkvr41q/ZNIGjrm1TV0X6kULWlgDU0WkyqXnYZFu4pROmvKdXBs6jeQH+vWy4oCalA0PljeKMsH4h6gzCXcc3hC5XOi+P4cFB/cD/zY/wCQtQ8h5NHJ6gAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100% 100%;
}

.fenxiang {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoAgMAAADxkFD+AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAMUExURUdwTKCluqKlu6Cluo5mgRgAAAADdFJOUwD0RhUZBMIAAABjSURBVBjTvdCxDcAgDERRiyZyxRislNJTULMIUkaiZAw2MAbERakT5VdPsqsjeh+rNq8qxmOxGP28hQZm0EWQKxgEzGnTRdrkCgYBc9q019ngCdJ1kx5s/9AmGZU11Eg+2LwDxuw0w7I5/ckAAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.zan-active {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA2UExURUdwTEm4/0Cj/0e4/0m4/0q5/0m4/0e4/0q4/0m4/0W2/0m4/0m4/0m4/0m4/0m4/0q4/////w+2YMcAAAAQdFJOUwBpCS/XXEUgrvQVupdzf5MhxBEqAAAAkklEQVQ4y+XU2w6DIBBFUeQ+YvXw/z9b2rSJY0XOq+l+JCshAwRj7liIlnIO8ImBC5qcx26WBvEYw/XlkIfOCgknkNCTMIKEhYTu41DSN9s97EMy/bJaBSc5NURo1Xrm1C3l90oH7vfGFYwsTCQUQ0LPwszClYWBhY6FloRq6CtYWLixUD9H6ULRf8YiHeij+e+eCYMkrqs+WS0AAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.wuxin-active {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA8UExURUdwTP+4O/+5Pf+5Pv+hPv+6Pf+5Pv+4Pf+4Pv+8Ov+5Pv+4Pf+5Pv+sQP+5Pf+NQP+5Pf+4Pv+5Pv+5PnaNIbYAAAATdFJOUwAuxV0HP9m/4x1vUdENhASynvVUkZFaAAAA20lEQVQ4y83URxLDIAwFUJotU1y5/13jxpBEP0FLs/XjM5IwSj1pzdMkg13OnQhSziQMlEXSAUkYKImkC5IwsB1JBZIw8H+k06lCo0ckYlhpyF9roDXE6gMX396e0OTmuirzQ8sNXolkcS3Z+1q172VOqfFnReQ/OzkSdoZ13S3ILQ5MB5xuHJr0yuEKr0TiMMHfeeNwmwHUqBgPYEQwAjghiB6X2sdUy1oALOPu9+NioT134/3FXoXae5/DRRd2DMqed08zaPcLGt7bNruwU8ugp4kd44LRD3jZX1woKjhfIVWoAAAAAElFTkSuQmCC') no-repeat;
  background-size: 100% 100%;
}