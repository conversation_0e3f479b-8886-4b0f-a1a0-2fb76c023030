/**
 * HuntCopy 搜索组件
 * 优化后的企业搜索组件副本，使用基础行为和策略模式
 * 与 hunt 组件的区别：排除了产业链相关字段
 */

const {renderList} = require('../hunt/mixin.js');
const {SearchStrategyFactory} = require('../hunt/common/searchStrategies.js');
const DataProcessor = require('../hunt/common/dataProcessor.js');
const BaseSearchBehavior = require('../hunt/common/baseSearchBehavior.js');
const {dateTagList} = require('../hunt/common/changlian.js');

// 设置日期标签列表到策略工厂
SearchStrategyFactory.setDateTagList(dateTagList);

// 过滤数据，排除产业链相关字段
const searchDataList = DataProcessor.filterDataByComponent(
  renderList,
  'huntCopy'
);

Component({
  behaviors: [BaseSearchBehavior],

  data: {
    itemList: JSON.parse(JSON.stringify(searchDataList)), // 页面静态数据
    leftList: JSON.parse(JSON.stringify(searchDataList)) // 页面静态数据-左边
  },

  methods: {
    /**
     * 获取组件类型
     * @returns {string} 组件类型
     */
    getComponentType() {
      return 'huntCopy';
    },

    /**
     * 获取过滤后的渲染列表
     * @returns {Array} 过滤后的列表
     */
    getFilteredRenderList() {
      return DataProcessor.filterDataByComponent(renderList, 'huntCopy');
    }
  },

  pageLifetimes: {
    show() {
      const {wrapHeight, isIphoneX} = this.data;

      // 计算容器高度
      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        });
      }

      this.setBackfillData(this.data.paramsData);
    }
  },

  observers: {
    vipVisible(val) {
      // 通过事件传递的方式告诉外面，需要vip弹窗
      console.log('是否需要弹vip弹窗', val);
      this.triggerEvent('vip', true);
    }
  }
});
