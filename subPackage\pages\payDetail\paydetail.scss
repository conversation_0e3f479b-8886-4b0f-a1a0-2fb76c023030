.pay {
  height: 100vh;
  width: 100vw;
  /* background: #f7f7f7; */
  position: relative;
  overflow: hidden;
  background: #f7f7ff;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.head_bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 130px;
  width: 100%;
}

/* 个人信息 */
.grxx {
  display: flex;
  position: relative;
  z-index: 1;
  height: 270rpx;
  width: 702rpx;
  margin: 0 auto 0;
  box-shadow: inset 0rpx 2rpx 0rpx 0rpx rgba(255, 255, 255, 0.8), inset 0rpx 8rpx 8rpx 0rpx rgba(255, 255, 255, 0.3);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  overflow: hidden;
  background: #fff;
  align-items: center;
  padding-left: 40rpx;
  transform: translateY(20rpx);
}

.grxx .dbg {
  position: absolute;
  left: 0;
  right: 0;
  top: -50rpx;
  border: 0;
  width: 726rpx;
  height: 354rpx;
}

.grxx-r {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.grxx-l {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 24rpx;
  height: 100rpx;
}

.grxx-l-t {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #573709;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 520rpx;
}

.grxx-l-b-l {
  flex-shrink: 0;
  width: 124rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.grxx-l-b-r {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(87, 55, 9, 0.8);
}

/* vip 团队套餐 */
.combo {
  position: relative;
  width: 100%;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  overflow: hidden;
  z-index: 11;
  transform: translateY(-20rpx);
  padding: 40rpx 0rpx;
}

.tab {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 174rpx;
}

.tab-i {
  position: relative;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #74798C;
  padding-bottom: 24rpx;
}

.tactive {
  color: #573709;
}

.tactive::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #573709;
}

/* vip */
.vipWrap {
  padding: 32rpx 0 40rpx;
  width: 100%;
  overflow-x: scroll;
  scroll-behavior: smooth;
}

.vipWrap::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.henWrap {
  display: inline-flex;
}

.vipWrap .item {
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  width: 164rpx;
  height: 232rpx;
  background: #FFFFFF;
  box-shadow: 0px 2rpx 40rpx 0px rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  overflow: hidden;
  margin-right: 16rpx;
  box-sizing: border-box;
}


.vipWrap .item:nth-of-type(1) {
  margin-left: 24rpx;
}

.vipWrap .item:last-child {
  margin-right: 22rpx;
}

.vipWrap .item .gg {
  position: absolute;
  width: 36rpx;
  height: 36rpx;
  right: 0;
  top: 0;
  opacity: 0;
}

.vipWrap .item .one {
  font-size: 28rpx;
  font-weight: 400;
  color: #20263A;
  padding-top: 32rpx;
  text-align: center;
}

.vipWrap .item .two {
  font-size: 28rpx;
  font-weight: 600;
  color: #20263A;
  padding-top: 20rpx;
}

.vipWrap .item .two text {
  font-weight: bold;
  font-size: 40rpx;
}

.vipWrap .item .three {
  font-size: 24rpx;
  font-weight: 400;
  color: #C7975B;
  padding-top: 18rpx;
}

.henWrap .lactive {
  background: #FCF9F2;
  border: 4rpx solid #EAC89E;
}

.henWrap .lactive .one {
  color: #573709;
}

.henWrap .lactive .two {
  color: #573709;
}

.vipWrap .lactive .gg {
  opacity: 1;
}

.vipWrap .items {
  width: 222rpx;
  height: 232rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 2rpx 40rpx 0rpx rgba(0, 0, 0, 0.1);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-right: 18rpx;
}

.vipWrap .items .one {
  padding-top: 32rpx;
  text-align: center;
}

.vipWrap .items .two {
  padding-top: 20rpx;
}

.vipWrap .items .three {
  padding-top: 20rpx;
}

.calculate {
  width: 702rpx;
  background: #F7F7F7;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin: 0rpx auto 40rpx;
  padding: 24rpx 24rpx 0;
}

.calculate .ones {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}

.calculate .twos {
  display: flex;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  padding-top: 16rpx;
}

.calculate .twos view:nth-of-type(2) {
  color: #20263A;
}

.threes {
  display: flex;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  padding: 20rpx 0 36rpx;
}

.fours {
  display: flex;
  border-top: 1rpx solid #DEDEDE;
  height: 104rpx;
  align-items: center;
  flex-wrap: wrap;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}

.fours .num {
  font-weight: 600;
  color: #C7975B;
  font-size: 28rpx;
}

.fours .num text {
  font-size: 32rpx;
  margin-left: 8rpx;
  margin-right: 2rpx;
}

.number {
  display: flex;
  border: 1px solid #dedede;
  width: 184rpx;
  background: #fff;
  border-radius: 8rpx;
}

.number .reduce {
  display: flex;
  border-right: 1px solid #dedede;
  width: 48rpx;
  height: 100%;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.number .reduce .no {
  width: 20rpx;
  height: 20rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAFRJREFUOE9jZKAyYKSyeQyjBlIeokMoDOcv3WVAiYcTo90ugPTDvbxg6a7/lBiYEO0GNot2Bi5esU+fEhfGRjhdRHEhJYYh6x1CyWbUy2SHANVjGQBkQxAVoAE6ZQAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100% 100%;
}


.number .reduce .yes {
  width: 20rpx;
  height: 20rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAFNJREFUOE9jZKAyYKSyeQyjBlIeokMoDBVUrQwo8fCD28cugPTDvaygZvWfIgNvHQObRTsDFdVs9Clx4f1bRy6iuJASw5D1DqFkM+plskOA6rEMAONlEBWTovRjAAAAAElFTkSuQmCC') no-repeat;
  background-size: 100% 100%;
}

.number .add {
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1rpx solid #dedede;
  width: 48rpx;
  height: 100%;
  flex-shrink: 0;
}

.number .add view {
  width: 20rpx;
  height: 20rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAG9JREFUOE9j/P///38GKgJGuht47fptBu+ABLAftm5YwKClqYrXPwRdOGrgAIYhKPCxgbv3HjLkFdWDpSb1NTIoK8ljVQeLfXgsK6pbU5S87988CtZPOwOp7mVc/h1N2AOYsOkWKaSmdoLlIakGAgBMHq/FAmwBcgAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100% 100%;
}

.number .numss {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
}

.paytype {
  background: #fff;
  padding: 0 24rpx;
}

.paytype .tit {
  height: 48rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.paycont {
  display: flex;
  justify-content: space-between;
}

.paycont .item {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
  align-items: center;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  opacity: 1;
  border: 1rpx solid #DEDEDE;
  height: 104rpx;
  width: 340rpx;
}

.paycont .item .item-l {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  flex: 1;
}

.paycont .pactive {
  background: #FCF9F2;
  border: 2rpx solid #EAC89E;
}

.paycont .item .item-l image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.item-r {
  width: 32rpx;
  height: 32rpx;
}

.item-r image {
  width: 100%;
  height: 100%;
}

/* 支付按钮 */
.btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 702rpx;
  height: 96rpx;
  margin: 40rpx auto 32rpx;
  background: linear-gradient(to left, rgba(234, 189, 136, 1), rgba(250, 229, 184, 1));
}

.btn view:nth-of-type(1) {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #573709;
  line-height: 38rpx;
  padding-top: 8rpx;
}

.btn view:nth-of-type(2) {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(87, 55, 9, 0.8);
  line-height: 36rpx;
}

.agreement {
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}

.agreement text {
  color: rgba(199, 151, 91, 1);
}

.special {
  width: 100%;
  background: #FFFFFF;
  margin-top: 48rpx;
  border-top: 20rpx solid #f7f7f7;
}

.special .tit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #573709;
  width: 100%;
  padding-top: 40rpx;
  margin-bottom: 40rpx;
}

.special .tit view {
  padding: 0 24rpx;
}

.special .tit .img1 {
  width: 46rpx;
  height: 17.32rpx;
}

.special .tit .img2 {
  width: 46rpx;
  height: 17.32rpx;
}

.slist {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 24rpx 8rpx;
}

.slist .itms {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 25%;
  height: 152rpx;
  margin-bottom: 32rpx;
}

.itms image {
  width: 80rpx;
  height: 80rpx;
}

.itms view:nth-of-type(1) {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  padding-top: 8rpx;
}

.itms view:nth-of-type(2) {
  font-size: 20rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #C7975B;
}

/* 支付 */
.team {
  position: relative;
  width: 750rpx;
  border-bottom: 20rpx solid #f7f7f7;
}

.team .tbgs {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100%;
}

.team .ttwo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #9f6419;
  align-items: center;
  padding-top: 8rpx;
}

.team .ttwo .imgperson {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}

.team .tthree {
  position: relative;
  padding-top: 10rpx;
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #c7975b;
  line-height: 28rpx;
}

.team .tfour {
  position: relative;
  display: flex;
  justify-content: center;
  padding-top: 28rpx;
}

.team .tfour .tbg {
  width: 670rpx;
  height: 420rpx;
}