/* input */
.pages {
  height: 100vh;
  overflow: hidden;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* input */
.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 28rpx 24rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #E72410;
  color: #74798c;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
  /* border-right: 1px solid hsla(229, 9%, 64%, 0.5); */
  /* border: 1px solid red; */
}

.s-input-item::after {
  content: "";
  height: 64rpx;
  width: 2px;
  background: #DEDEDE;
  /* background: red; */
  position: absolute;
  right: 0;
  /* top: 50%; */
  transform: scale(0.5);
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.search-cancel {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
  padding: 0 28rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  /* border: 1px solid red; */
}

/* 最近搜索和浏览历史样式  */

.page__autofit {
  padding: 28rpx 32rpx 28rpx 32rpx;
}

.search_b {
  padding-bottom: 0 !important;
}

.his_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.his_titles {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.his_title_l {
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.his_title_icon {
  width: 40rpx;
  height: 40rpx;
}

.his_content {
  display: flex;
  width: 100%;
  overflow: hidden;
  /* border: 1px solid red; */
  /* height: 168rpx; */
  height: 160rpx;
}

.text-box {
  text-align: justify;
  display: flex;
  flex-wrap: wrap;
  /* text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical; */
}

.his_content_item {
  /* display: inline-flex; */
  background: #f5f6f7;
  border-radius: 100rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
  padding: 8rpx 20rpx;
  min-width: 96rpx;
  max-width: 680rpx;
  height: 56rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  justify-content: center;
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
}

/* 暂无历史搜索 */
.his_content_none {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 104rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.his_content1_item {
  height: 80rpx;
}

/* 筛选组件 */

/* 缺省页 */
.queshen {
  width: 100%;
}

/* 卡片  */
.card-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 100rpx;
}

.card-box-text {
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  background: #fff;
  border: 1px;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
  border-top: 1px dashed #eeeeee;
}

.his_content1_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  height: auto;
}

.his_content1_item-l {
  flex: 1;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.his_content1_item-r {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #9b9eac;
  min-width: 80rpx;
  flex-shrink: 0;
}