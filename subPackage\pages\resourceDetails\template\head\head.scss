.flex {
  display: flex;
  flex-direction: column;
}
.mgt8 {
  margin-top: 8rpx;
}
.ellipsis {
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.box {
  padding: 40rpx 32rpx;
  background-color: #fff;
  display: flex;
}
.box .img {
  width: 136rpx;
  height: 136rpx;
  flex-shrink: 0;
  border-radius: 8rpx;
}
.box .desc {
  margin-left: 24rpx;
}
.box .desc {
  flex: 1;
  margin-left: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
  font-size: 24rpx;
}
.box .desc .tit {
  font-size: 36rpx;
  font-weight: 600;
  color: #20263A;
}

.box .desc .label-wrap {
  display: flex;
  margin-top: 8rpx;
}
.box .desc .label-wrap .label-left {
  width: 256rpx;
}
.box .desc .label-wrap .label-right {
  flex: 1;
  max-width: 278rpx;
}
.box .desc .label-wrap .label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
}
.box .desc .label-wrap .label text {
  color: #9B9EAC;
}

.experts .desc .tit {
  font-weight: 400;
}
.experts .desc .tit .sex {
  font-size: 30rpx;
  color: #9B9EAC;
  margin-left: 10rpx;
}

.w232 {
  width: 232rpx;
}
.maxW310 {
  max-width: 310rpx;
}
.maxW542 {
  max-width: 542rpx;
}
.transform {
  padding: 40rpx 32rpx;
  background-color: #fff;
  display: flex;
}
.transform .logo {
  width: 136rpx;
  height: 136rpx;
  flex-shrink: 0;
}
.transform .desc {
  flex: 1;
  margin-left: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
  font-size: 24rpx;
}
.transform .desc .tit {
  font-size: 36rpx;
  font-weight: 400;
  color: #20263A;
}
.transform .desc .label-wrap {
  display: flex;
  margin-top: 8rpx;
  flex-wrap: wrap;

}
.transform .desc .label-wrap .label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
}
.transform .desc .label-wrap .label text {
  color: #9B9EAC;
}