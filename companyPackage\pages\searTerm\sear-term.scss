@import '../../../template/null/null.scss';

.searpages {
  width: 100%;
  height: 100vh;
  position: relative;
}

/* 筛选相关 */
.footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  background-color: #fff;
  /* border-top: 2rpx solid #f5f5f5; */
  display: flex;
  justify-content: space-between;
  padding: 10rpx 24rpx;
  box-shadow: 8px 0px 8px 2px rgba(204.00000303983688, 204.00000303983688, 204.00000303983688, 0.20000000298023224);
}

.footer text {
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  /* background: #E72410; */
  background: linear-gradient(90deg, #FFB2AA 0%, #E72410 100%);
  color: #fff;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  /* font-weight: 600; */
}

.footer text:nth-child(1) {
  color: #74798C;
  background: linear-gradient(315deg, #EEEEEE 0%, #F5F5F5 100%);
}

.footer text:nth-child(2) {
  color: #74798C;
  background: linear-gradient(315deg, #EEEEEE 0%, #F5F5F5 100%);
}

.company-filter {
  /* height: 100%; */
  display: flex;
  flex-direction: column;
  position: relative;
  background: #FFFFFF;
}


.content {
  flex: 1;
  background-color: #fff;
  padding: 0 21rpx 50rpx;
  margin-top: 1px;
  overflow-y: auto;
}

.content .content-item-pop {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 92rpx;
  align-items: center;
}

.content .content-item-pop::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: scaleY(0.5);
}

.content .content-item-pop-r {
  display: flex;
  align-items: center;
}

.content .content-item-pop-r text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.content .content-item-pop-r image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 28rpx;
}

.content .content-item-pop .title {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}

.content .content-item {
  font-size: 25rpx;
  color: #5C6070;
  padding: 60rpx 0 0rpx;
  background-color: #fff;
}

.content-item .tit {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #20263a;
}

/* 企业规模说明--icon*/
.filterexp {
  display: inline-block;
  margin-left: 12rpx;
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAAXNSR0IArs4c6QAAANhQTFRFAAAAgICAn5+flZWqn5+vmZmzn5+1m5u2paW9n5+3oKi9nKO4o6O+n5+5o6O4o6i4oKW5oqe5oqa6n6O3oqW7oKO7oaW4oaW7oKa7oaS5oKW5oKW8oaS5oKW5oaO6oaa6oKS6oaW6oKS6oaW7oKW5oKS5oKa5oKS6oKW6oKW6oKS6oKW6oKS6oKW6oKS5oKW5oKS5oKW5oKS5oKS6oKW6oKW6oKS6oKW5oKW6oKS5oKS6oKW5oKW6oKS5oKW5oKW5oKS5oKS6oKW6oKW6oKS6oKW6oKW6oKW66g9uAAAAAEd0Uk5TAAQIDBAUGBwfICMkJygvLzM3P0BHS09PU1dbW19jZ2drb3N/i4+Pl5ebn5+jp6uvs7O3x8fLz8/P09PX19vf4+fr6/P39/vWY4YUAAABYklEQVQ4y32T2VrCMBBGxxIXrLivFVxBhSgoWhCLMWDsef838qaFtrbO1SwnmfmSf0QWpgI9nsM87AZK/tqm/mFh7n6rUPbaMRBHL1oPJgCu4+WOT4HX1mrSqzkGoo1lfceCOcyeODZgd9Now0K/Vuj5BHYz8afQ+Tt1ByIv9QZpdu/6ej/1+9AWEdlymMXEt5nLaobYF5F7OJYyQA5BiyjHeNn4YjS6WUZvOCUBtKTCWhBIF9aqgNWYnoSYTKp+cOBnwiljmTPMZG7zT/LCXEBXAxoEHv8H/m0xwMooN2QB+CAUDetVgIKunMNlFdCEM1GOjyrgHadEenBaDpzAg4j4MV+qDPAMriEi0obnlSS53WwulPiUwl5UKrk7mCRC8i08F0Rb64Otp9GuBXOSrR8ZsI3ML0fA+1UijLWLEJjUc2vQcQBmqB+HnwBx2yvM1Hhwy+X97vklAlNBN5zBLL/+v69tTgQXnGC4AAAAAElFTkSuQmCC");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.expPop {
  /* 企业规模说明弹窗 */
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  padding: 48rpx 32rpx 60rpx;
}


.content-item .wrap {
  display: flex;
  flex-wrap: wrap;

}

.content-item .wrap>text {
  padding: 0 20rpx;
  height: 56rpx;
  line-height: 56rpx;
  background-color: #F4F4F4;
  /* border: 2rpx solid #F4F4F4; */
  margin-right: 20rpx;
  margin-top: 24rpx;
  border-radius: 4rpx;
  font-weight: 400;
}

.content-item .wrap text.active {
  /* border: 2rpx solid #E72410; */
  color: #E72410;
  background: rgba(7, 110, 228, 0.16);
}

.content-item .wrap .input-wrap {
  display: flex;
  align-items: center;
  margin-top: 25rpx;
}

.content-item .wrap .input-wrap>view {
  display: flex;
  width: 350rpx;
  height: 56rpx;
  padding: 0 19rpx;
  background-color: #F4F4F4;
  border-radius: 4rpx;
  margin-right: 19rpx;
  color: #9B9EAC;

}

.content-item .wrap .input-wrap>view .year {
  flex-shrink: 0;
  line-height: 56rpx;
}

.content-item .wrap .input-wrap>view input {
  height: 100%;
}

.content-item .wrap .input-wrap>view .short-line {
  width: 200rpx;
  height: 100%;
  line-height: 52rpx;
  text-align: center;
}

.content-item .wrap .input-wrap>view.active {
  /* border: 2rpx solid #E72410; */
  color: #E72410 !important;
  background-color: rgba(7, 110, 228, 0.10);
}

/* 保存选项弹窗 */
.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mask {
  position: fixed;
  z-index: 99;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  /* display: flex;
    justify-content: center;
    align-items: center; */
}

.mask .box {
  width: 604rpx;
  height: 354rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  opacity: 1;
  margin: 434rpx auto 0;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.mask .box .tit {
  padding: 48rpx 0 24rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A
}

.mask .box .inpt {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 524rpx;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  padding: 24rpx 28rpx;
}

.mask .box .inpt>input {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #5B5F6F;
}

.placeholder-input {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.mask .box .btn {
  position: relative;
  display: flex;
  height: 100rpx;
  width: 100%;
  margin-top: 48rpx;
}

.mask .box .btn::before {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.mask .box .btn::after {
  content: " ";
  width: 1px;
  height: 100rpx;
  background: #eee;
  position: absolute;
  top: 0;
  left: 50%;
  transform: scaleX(0.5) translateX(-50%);
}

.mask .box .btn view {
  flex: 1;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 100rpx;
  text-align: center;
}

/* 搜索模版 */
.sear-con {
  padding: 0 24rpx;
}

/* 216 */
.sear-con-h {
  position: relative;
  padding: 60rpx 0 32rpx;
}

.sear-con-h-box {
  max-height: 216rpx;
  overflow: hidden;
}

.sear-con-h::before {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.sear-con-h::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: scaleY(0.5);
}

.sear-con-h .item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  background: #F4F4F4;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  padding: 0 28rpx;
  margin-bottom: 28rpx;
}

.sear-con-h .item .item-l {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #5B5F6F;
  /* border: 1px solid red; */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.sear-con-h .item .item-r {
  width: 40rpx;
  height: 40rpx;
}

.sear-con-h .active {
  background: #E7F1FD !important;
}

.sear-con-h .active .item-l {
  color: #E72410;
  font-weight: 600;
}

.sear-con-b {
  padding: 40rpx 24rpx 52rpx;
}

.sear-con-b .title {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #333333;
  padding-bottom: 24rpx;
}


.sear-con-b .tagbox {
  height: 300rpx;

}

.sear-con-b .tagbox .wrap {
  display: flex;
  flex-wrap: wrap;
  max-height: 100%;
}

.sear-con-b .tagbox .tag {
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  margin-bottom: 28rpx;
  /* height: 56rpx; */
  text-align: left;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 2rpx solid#E72410;
  color: #E72410;
  font-size: 26rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  max-height: 85rpx;
}

/* 搜索按钮 */
.s-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  right: 32rpx;
  bottom: 300rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #FFB2AA 0%, #E72410 100%);
  box-shadow: 0px 4rpx 4rpx 2rpx rgba(16, 19, 29, 0.1);
  border-radius: 50%;
  overflow: hidden;
  z-index: 1;
}

.s-btn image {
  width: 40rpx;
  height: 40rpx;
}

.s-btn .txt {
  font-size: 20rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
}

.content-item .wrap .input-wrap view.active .plc {
  color: #E72410 !important;
}