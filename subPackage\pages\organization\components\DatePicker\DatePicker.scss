/* components/TimePicker/TimePicker.scss */
.date-wrap {
  width: 100%;
  height: 450rpx;
  border-top: 2rpx solid #f5f5f5;
  /* border-bottom: 2rpx solid #f5f5f5; */
}

.date-wrap .indicator {
  background: rgba(7, 110, 228, .1);
  height: 70rpx;
  font-weight: 500;
  color: #E72410 !important;
  font-size: 31rpx;
  /* border: none !important; */
  border: 1rpx solid transparent !important;
  border-color: transparent !important;
}

.indicator {
  border: 1rpx solid transparent !important;
  border-color: transparent !important;
}

.indicator::after {
  border-bottom: 1rpx solid transparent !important;
  border-color: transparent !important;
}

.indicator::before {
  border-top: 1rpx solid transparent !important;
  border-color: transparent !important;
}

.date-wrap .column {
  line-height: 70rpx;
  text-align: center;
  font-size: 31rpx;
}

.date-wrap .column.active {
  color: rgba(231, 36, 16, 1);
  font-weight: 500;
}
.maskClass {
  background: rgba(231, 36, 16, 0.1);
}