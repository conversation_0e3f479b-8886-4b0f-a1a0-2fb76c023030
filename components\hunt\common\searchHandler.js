/**
 * 搜索处理器
 * 简化的搜索逻辑处理
 */

const { getFieldType, getFieldConfig } = require('./config.js');

/**
 * 搜索处理器类
 */
class SearchHandler {
  /**
   * 处理标签选择
   * @param {Object} params - 当前参数
   * @param {string} fieldKey - 字段键
   * @param {string} tagId - 标签ID
   * @param {Array} itemList - 项目列表
   * @returns {Object} 更新后的参数和列表
   */
  static handleTagSelect(params, fieldKey, tagId, itemList) {
    const fieldType = getFieldType(fieldKey);
    
    switch (fieldType) {
      case 'radio':
        return this.handleRadioSelect(params, fieldKey, tagId, itemList);
      case 'multiSelect':
        return this.handleMultiSelect(params, fieldKey, tagId, itemList);
      case 'rangeInput':
        return this.handleRangeSelect(params, fieldKey, tagId, itemList);
      case 'datePop':
        return this.handleDateSelect(params, fieldKey, tagId, itemList);
      default:
        return { params, itemList };
    }
  }

  /**
   * 处理单选
   */
  static handleRadioSelect(params, fieldKey, tagId, itemList) {
    const updatedItemList = itemList.map(item => {
      if (item.type === fieldKey) {
        item.list = item.list.map(tag => {
          tag.active = false; // 先清除所有选中状态
          if (tag.id === tagId) {
            if (params[fieldKey] == tagId) {
              tag.active = false;
              params[fieldKey] = [];
            } else {
              tag.active = true;
              params[fieldKey] = [tagId];
            }
          }
          return tag;
        });
      }
      return item;
    });

    return { params, itemList: updatedItemList };
  }

  /**
   * 处理多选
   */
  static handleMultiSelect(params, fieldKey, tagId, itemList) {
    const updatedItemList = itemList.map(item => {
      if (item.type === fieldKey) {
        item.list = item.list.map(tag => {
          if (tag.id === tagId) {
            const arr = params[fieldKey];
            if (arr.includes(tagId)) {
              arr.splice(arr.findIndex(i => i === tagId), 1);
              tag.active = false;
            } else {
              arr.push(tagId);
              tag.active = true;
            }
          }
          return tag;
        });
      }
      return item;
    });

    return { params, itemList: updatedItemList };
  }

  /**
   * 处理范围选择
   */
  static handleRangeSelect(params, fieldKey, tagId, itemList) {
    const obj = this.parseRangeValue(tagId);
    
    // 清除自定义输入的数据
    params[fieldKey] = params[fieldKey].filter(i => !i.special);
    
    const updatedItemList = itemList.map(item => {
      if (item.type === fieldKey) {
        item.list = item.list.map(tag => {
          if (tag.id === tagId) {
            const arr = params[fieldKey];
            const idx = arr.findIndex(i => 
              JSON.stringify(i) === JSON.stringify(obj)
            );
            
            if (idx >= 0) {
              arr.splice(idx, 1);
              tag.active = false;
            } else {
              arr.push(obj);
              tag.active = true;
            }
          }
          return tag;
        });
      }
      return item;
    });

    return { 
      params, 
      itemList: updatedItemList,
      clearInputs: true
    };
  }

  /**
   * 处理日期选择
   */
  static handleDateSelect(params, fieldKey, tagId, itemList) {
    return this.handleRangeSelect(params, fieldKey, tagId, itemList);
  }

  /**
   * 解析范围值
   * @param {string} str - 范围字符串 "start$end"
   * @returns {Object} 解析后的对象
   */
  static parseRangeValue(str) {
    const sign = '$';
    const obj = { start: '', end: '' };
    
    if (str.indexOf(sign) > -1) {
      const arr = str.split(sign);
      obj.start = arr[0];
      obj.end = arr[1];
    }
    
    return obj;
  }

  /**
   * 处理自定义范围输入
   * @param {Object} params - 参数对象
   * @param {string} fieldKey - 字段键
   * @param {string} start - 开始值
   * @param {string} end - 结束值
   * @returns {Object} 更新后的参数
   */
  static handleCustomRangeInput(params, fieldKey, start, end) {
    params[fieldKey] = [{
      start,
      end,
      special: true
    }];
    return params;
  }

  /**
   * 验证范围输入
   * @param {Object} params - 参数对象
   * @returns {Object} 验证结果
   */
  static validateRangeInputs(params) {
    const rangeFields = ['register_capital', 'super_dimension_social_num', 'register_time'];
    
    for (const fieldKey of rangeFields) {
      if (params[fieldKey] && params[fieldKey].length === 1) {
        const item = params[fieldKey][0];
        if (item.special) {
          const config = getFieldConfig(fieldKey);
          
          if (fieldKey === 'register_time') {
            const startTime = new Date(item.start).getTime();
            const endTime = new Date(item.end).getTime();
            
            if (startTime >= endTime) {
              return {
                isValid: false,
                message: `${config.minLabel}不能大于等于${config.maxLabel}`
              };
            }
          } else {
            if (parseFloat(item.start) >= parseFloat(item.end)) {
              return {
                isValid: false,
                message: `${config.minLabel}不能大于等于${config.maxLabel}`
              };
            }
          }
        }
      }
    }
    
    return { isValid: true };
  }

  /**
   * 处理回填数据
   * @param {Object} params - 参数对象
   * @param {string} fieldKey - 字段键
   * @param {*} value - 回填值
   * @returns {Object} 处理结果
   */
  static handleBackfill(params, fieldKey, value) {
    const fieldType = getFieldType(fieldKey);
    
    switch (fieldType) {
      case 'rangeInput':
      case 'datePop':
        return this.handleRangeBackfill(fieldKey, value);
      default:
        return { tagId: value };
    }
  }

  /**
   * 处理范围回填
   */
  static handleRangeBackfill(fieldKey, value) {
    if (!value.length) return { tagId: value };
    
    const str = value[0].start + '$' + value[0].end;
    const config = getFieldConfig(fieldKey);
    
    // 检查是否为自定义输入
    if (value[0].special) {
      return {
        tagId: str,
        customInput: {
          min: value[0].start,
          max: value[0].end,
          active: true
        }
      };
    }
    
    return { tagId: value };
  }

  /**
   * 清除标签状态
   * @param {string} fieldKey - 字段键
   * @param {Array} itemList - 项目列表
   * @param {Object} params - 参数对象
   * @returns {Object} 更新结果
   */
  static clearTagStatus(fieldKey, itemList, params) {
    const updatedItemList = itemList.map(item => {
      if (item.type === fieldKey) {
        item.list = item.list.map(tag => {
          tag.active = false;
          return tag;
        });
      }
      return item;
    });
    
    params[fieldKey] = [];
    
    return { itemList: updatedItemList, params };
  }
}

module.exports = SearchHandler;
