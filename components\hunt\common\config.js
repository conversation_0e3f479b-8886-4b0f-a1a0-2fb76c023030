/**
 * 搜索组件配置文件
 * 简化配置，支持动态添加和修改筛选项
 */

// 默认搜索参数
const DEFAULT_PARAMS = {
  ent_name: '',
  regionData: [],
  eleseic_data: [],
  technology_types: [],
  listed_status: [],
  super_dimension_patent_category: [],
  expand_status: [],
  ent_scale: [],
  benefit_assess: [],
  register_time: [],
  register_capital: [],
  ent_status: [],
  shit_type: [],
  enttype_data: [],
  all_cert_data: [],
  contact_style: [],
  tel_data: [],
  email_data: [],
  super_dimension_social_num: [],
  super_dimension_biding: [],
  super_dimension_job_info: [],
  tax_credit: [],
  financing_info: [],
  super_dimension_trademark: [],
  super_dimension_patent: [],
  super_dimension_android_app: [],
  super_dimension_ios_app: [],
  super_dimension_mini_app: [],
  super_dimension_wx_extension: [],
  super_dimension_weibo_extension: [],
  untrustworthy_info_data: [],
  judgment_doc_data: [],
  adminstrative_penalties_data: [],
  chattel_mortage_data: [],
  abnormal_operation_data: [],
  software_copyright_data: [],
  work_copyright_data: [],
  super_dimension_website: [],
  super_dimension_icp: [],
  chain_codes_data: [],
  leading_ent: []
};

// 筛选字段配置 - 通过类型分组，便于管理和扩展
const FIELD_CONFIGS = {
  // 输入框类型
  input: {
    ent_name: { 
      label: '企业名称', 
      placeholder: '请输入企业名称',
      maxLength: 50
    }
  },
  
  // 单选类型 - 有/无选择
  radio: {
    tel_data: { label: '联系方式' },
    contact_style: { label: '手机号码' },
    email_data: { label: '联系邮箱' },
    super_dimension_biding: { label: '招投标' },
    super_dimension_job_info: { label: '招聘' },
    tax_credit: { label: '纳税信用' },
    super_dimension_trademark: { label: '商标信息' },
    super_dimension_patent: { label: '专利信息' },
    super_dimension_android_app: { label: '安卓APP' },
    super_dimension_ios_app: { label: '苹果APP' },
    super_dimension_mini_app: { label: '小程序' },
    super_dimension_wx_extension: { label: '微信公众号' },
    super_dimension_weibo_extension: { label: '微博' },
    untrustworthy_info_data: { label: '失信信息' },
    judgment_doc_data: { label: '裁判文书' },
    adminstrative_penalties_data: { label: '行政处罚' },
    chattel_mortage_data: { label: '动产抵押' },
    abnormal_operation_data: { label: '经营异常' },
    software_copyright_data: { label: '软件著作权' },
    work_copyright_data: { label: '作品著作权' },
    super_dimension_website: { label: '官网信息' },
    super_dimension_icp: { label: 'ICP备案' },
    leading_ent: { label: '龙头企业' }
  },
  
  // 多选类型
  multiSelect: {
    capital_event: { label: '资本事件' },
    shit_type: { label: '实体类型' },
    ent_status: { label: '企业状态' },
    technology_types: { label: '科技型企业' },
    financing_info: { label: '融资信息' },
    listed_status: { label: '上市状态' },
    super_dimension_patent_category: { label: '专利内容' },
    expand_status: { label: '疑似扩张' }
  },
  
  // 范围输入类型
  rangeInput: {
    register_capital: {
      label: '注册资本',
      minLabel: '最低资本',
      maxLabel: '最高资本',
      unit: '万元',
      type: 'number',
      maxLength: 10
    },
    super_dimension_social_num: {
      label: '参保人数',
      minLabel: '最低人数',
      maxLabel: '最高人数',
      unit: '人',
      type: 'number',
      maxLength: 5
    }
  },
  
  // 日期选择类型
  datePop: {
    register_time: {
      label: '注册时间',
      minLabel: '最低年限',
      maxLabel: '最高年限',
      unit: '年'
    }
  },
  
  // 弹窗选择类型
  pop: {
    areas: { 
      label: '所在地区', 
      dataKey: 'regionData',
      popType: 'region'
    },
    trade_types: { 
      label: '所属行业', 
      dataKey: 'eleseic_data',
      popType: 'industry'
    },
    ent_type: { 
      label: '企业类型', 
      dataKey: 'enttype_data',
      popType: 'entType'
    },
    ent_cert: { 
      label: '企业许可', 
      dataKey: 'all_cert_data',
      popType: 'cert'
    },
    chain_codes: { 
      label: '所属产业链', 
      dataKey: 'chain_codes_data',
      popType: 'chain'
    }
  }
};

// 组件差异配置
const COMPONENT_CONFIGS = {
  hunt: {
    name: '完整搜索组件',
    excludeFields: [],
    excludeFromCategories: {}
  },
  huntCopy: {
    name: '简化搜索组件',
    excludeFields: ['chain_codes'],
    excludeFromCategories: {
      '产业优选': ['chain_codes']
    }
  }
};

// 工具函数：获取字段类型
function getFieldType(fieldKey) {
  for (const [type, fields] of Object.entries(FIELD_CONFIGS)) {
    if (fields[fieldKey]) {
      return type;
    }
  }
  return null;
}

// 工具函数：获取字段配置
function getFieldConfig(fieldKey) {
  const type = getFieldType(fieldKey);
  return type ? FIELD_CONFIGS[type][fieldKey] : null;
}

// 工具函数：添加新的筛选字段
function addField(type, key, config) {
  if (!FIELD_CONFIGS[type]) {
    FIELD_CONFIGS[type] = {};
  }
  FIELD_CONFIGS[type][key] = config;
  
  // 同时添加到默认参数中
  if (!DEFAULT_PARAMS.hasOwnProperty(key)) {
    DEFAULT_PARAMS[key] = Array.isArray(config.defaultValue) ? [] : '';
  }
}

// 工具函数：移除筛选字段
function removeField(fieldKey) {
  const type = getFieldType(fieldKey);
  if (type && FIELD_CONFIGS[type][fieldKey]) {
    delete FIELD_CONFIGS[type][fieldKey];
    delete DEFAULT_PARAMS[fieldKey];
  }
}

// 工具函数：获取组件配置
function getComponentConfig(componentType) {
  return COMPONENT_CONFIGS[componentType] || COMPONENT_CONFIGS.hunt;
}

// 工具函数：过滤字段（根据组件类型）
function filterFields(componentType) {
  const config = getComponentConfig(componentType);
  const filteredFields = {};
  
  Object.keys(FIELD_CONFIGS).forEach(type => {
    filteredFields[type] = {};
    Object.keys(FIELD_CONFIGS[type]).forEach(key => {
      // 检查是否在排除列表中
      if (!config.excludeFields.includes(key)) {
        filteredFields[type][key] = FIELD_CONFIGS[type][key];
      }
    });
  });
  
  return filteredFields;
}

// 布尔类型字段列表（用于数据处理）
const BOOLEAN_FIELDS = [
  'super_dimension_biding', 'super_dimension_job_info', 'super_dimension_trademark', 
  'super_dimension_patent', 'super_dimension_android_app', 'super_dimension_ios_app', 
  'super_dimension_mini_app', 'super_dimension_wx_extension', 'super_dimension_weibo_extension', 
  'super_dimension_website', 'super_dimension_icp', 'leading_ent'
];

// 父级类型前缀（用于数据处理）
const PARENT_TYPE_PREFIXES = ['super_dimension_'];

module.exports = {
  DEFAULT_PARAMS,
  FIELD_CONFIGS,
  COMPONENT_CONFIGS,
  BOOLEAN_FIELDS,
  PARENT_TYPE_PREFIXES,
  
  // 工具函数
  getFieldType,
  getFieldConfig,
  addField,
  removeField,
  getComponentConfig,
  filterFields
};
