@import '/subPackage/template/wxParse/wxParse.scss';

.artics {
  position: relative;
  -webkit-user-select: none;
  user-select: none;
  background: #f2f2f2;
  width: 100vw;
  height: 100vh;
  scroll-behavior: auto;
  overflow-y: scroll;
  padding-bottom: 24rpx;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.ahead {
  padding: 40rpx 32rpx;
  background: #fff;
}

.ahead-t {
  display: flex;
}

.ahead-t .img {
  width: 136rpx;
  height: 136rpx;
  flex-shrink: 0;
}

.ahead-t-l {
  flex: 1;
  padding-left: 24rpx;
}

.title {
  font-size: 36rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 56rpx;
  text-align: left;
  padding-bottom: 16rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.subt {
  display: flex;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  align-items: center;
}

.subt text {
  color: #3D4255;
}

.subt .one {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 270rpx;
}

.dic {
  display: flex;
  background: rgba(74, 184, 255, 0.04);
  border-radius: 8rpx;
  opacity: 1;
  border: 1rpx solid rgba(74, 184, 255, 0.1);
  /* height: 120rpx; */
  margin-top: 40rpx;
}

.dic-l {
  width: 156rpx;
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
  border-right: 1rpx solid rgba(74, 184, 255, 0.1);
}

.dic-r {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #3D4255;
  padding: 0 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-height: 28px;
  overflow: hidden;
}

/* 基金简介 */
.jijin {
  background: #fff;
  margin-top: 20rpx;
}

.jijin-t {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding-left: 32rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  border-bottom: 1rpx solid #EEEEEE;
}

.jijin-c {
  padding: 40rpx 32rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 40rpx;
}

/* <!-- 投资企业名单 --> */
.touzi {
  background: #fff;
  margin-top: 20rpx;
}

.touzi-t {
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  display: flex;
  height: 100rpx;
  align-items: center;
  padding-left: 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.touzi-b {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
}

.touzi-b .img {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

.touzi-c {
  padding: 0 32rpx;
}

.touzi-item {
  height: 104rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}

.touzi-item .imgs {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
  margin-left: 20rpx;
}






/* 同类推荐 */
.tuijian {
  background: #fff;
  margin-top: 20rpx;
}

.tuijian-t {
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  display: flex;
  height: 100rpx;
  align-items: center;
  padding-left: 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.tuijian-c {
  padding: 40rpx 0;
  margin: 0 32rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.tuijian-title {
  max-height: 96rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 48rpx;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.tujina-sub {
  display: flex;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  flex-wrap: wrap;
}

.tujina-sub .one {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 40rpx;
}

.tujina-sub .two {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  /* margin-left: 40rpx; */
}


.tujina-sub .two .img {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  /* transform: translateY(2px); */
}

.tujina-sub .two-t {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}


/* foot */
.artic_foot {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  box-shadow: 8rpx 0rpx 8rpx 0rpx rgba(204, 204, 204, 0.20);
}

.artic_foot_wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  padding: 16rpx 32rpx 0;
}

.artic_foot_wrap-l {
  display: flex;
}

.artic_foot_item {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: relative;
  /* border: 1px solid red; */
}

.zhuanfa {
  margin-left: 76rpx;
}

.artic_foot_item_b {
  font-size: 20rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
  padding-top: 4rpx;
}

.btn {
  position: absolute;
  left: 0;
  right: 0;
  padding: 0;
  margin: 0 !important;
  height: 100%;
  width: 54px !important;
  background: transparent;
  z-index: 10;
}

.artic_foot_about {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(135deg, #4AB8FF 0%, #E72410 100%);
  border-radius: 8rpx;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 400;
  text-align: CENTER;
  color: #ffffff;
}


/* 图标 */
.zan {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAwUExURUdwTKGluaCluqCluqCluqCluqGhuKGlupycr6CluqGlu6Glu6GluqCluqCluqClujO7KToAAAAPdFJOUwBeoOH1yR92Da9HUC+QZ1eusQIAAADbSURBVCjPY2CgDji+A4tg/JcHGGJcX+K/Ywhyfuf7koAueN6DQb8AXfD+BQb5CWhibP4HGPajC7L/ZMAUbP6FRVA+AItgvABQcFpaWloGktM/NwAF/wPBFw0kpwOJxcZAYPIVItK1UbDeA+42+wVgrf5AXRcQvgD7i/WrkpK9AEIQHFj8PxgY9BGC8WDt/B9QBO0TMAU5vjBgCvJ8xSLI+BOLIPt3LIKcH7EIsv7BIsjyA4tgvgEWwfUBWATfK2ARhEY+C3KAcAGjGuyI/8jgL9S3IUhinwsY6AkAkzJowpj59cYAAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.wuxin {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA5UExURUdwTKCkuaCkuqGhtKKku6GluqKlupycpKCluaCluqGluqGluqGlu6GluqCluqCkuqCluqCluqClugNaeKIAAAASdFJOUwDmvxMfYD8J3tRTgi1vxPOasiI0u1MAAAEoSURBVDjLzZRbloQgDAUFQR4Cwt3/Yic8nFbE03w2Px5jUSExuiw/tfZ9jtuAbQrkAJ8TsmNKyeH3GSUJ1TqjJCHV/V2ZhcsyoSzCCWUVflUqZ6owK4NTI8L6yBkA3Z6umm4Yj95+eFmIvA4TxRkV0RwtzLgtoUCETlFuok8kNh+TwflKhUZQ7wdfA3RTEGnWV878c5UUY05cuW7bjeuTXQ7ScaE7lOID0o3KVAms36yRRm8nwN4j8qVtEbIH47ATph+YDWYIHugSrThGnIPuQxqj5lqkM+XW1Kkvr41q/ZNIGjrm1TV0X6kULWlgDU0WkyqXnYZFu4pROmvKdXBs6jeQH+vWy4oCalA0PljeKMsH4h6gzCXcc3hC5XOi+P4cFB/cD/zY/wCQtQ8h5NHJ6gAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100% 100%;
}

.fenxiang {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoAgMAAADxkFD+AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAMUExURUdwTKCluqKlu6Cluo5mgRgAAAADdFJOUwD0RhUZBMIAAABjSURBVBjTvdCxDcAgDERRiyZyxRislNJTULMIUkaiZAw2MAbERakT5VdPsqsjeh+rNq8qxmOxGP28hQZm0EWQKxgEzGnTRdrkCgYBc9q019ngCdJ1kx5s/9AmGZU11Eg+2LwDxuw0w7I5/ckAAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.zan-active {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA2UExURUdwTEm4/0Cj/0e4/0m4/0q5/0m4/0e4/0q4/0m4/0W2/0m4/0m4/0m4/0m4/0m4/0q4/////w+2YMcAAAAQdFJOUwBpCS/XXEUgrvQVupdzf5MhxBEqAAAAkklEQVQ4y+XU2w6DIBBFUeQ+YvXw/z9b2rSJY0XOq+l+JCshAwRj7liIlnIO8ImBC5qcx26WBvEYw/XlkIfOCgknkNCTMIKEhYTu41DSN9s97EMy/bJaBSc5NURo1Xrm1C3l90oH7vfGFYwsTCQUQ0LPwszClYWBhY6FloRq6CtYWLixUD9H6ULRf8YiHeij+e+eCYMkrqs+WS0AAAAASUVORK5CYII=') no-repeat;
  background-size: 100% 100%;
}

.wuxin-active {
  width: 40rpx;
  height: 40rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA8UExURUdwTP+4O/+5Pf+5Pv+hPv+6Pf+5Pv+4Pf+4Pv+8Ov+5Pv+4Pf+5Pv+sQP+5Pf+NQP+5Pf+4Pv+5Pv+5PnaNIbYAAAATdFJOUwAuxV0HP9m/4x1vUdENhASynvVUkZFaAAAA20lEQVQ4y83URxLDIAwFUJotU1y5/13jxpBEP0FLs/XjM5IwSj1pzdMkg13OnQhSziQMlEXSAUkYKImkC5IwsB1JBZIw8H+k06lCo0ckYlhpyF9roDXE6gMX396e0OTmuirzQ8sNXolkcS3Z+1q172VOqfFnReQ/OzkSdoZ13S3ILQ5MB5xuHJr0yuEKr0TiMMHfeeNwmwHUqBgPYEQwAjghiB6X2sdUy1oALOPu9+NioT134/3FXoXae5/DRRd2DMqed08zaPcLGt7bNruwU8ugp4kd44LRD3jZX1woKjhfIVWoAAAAAElFTkSuQmCC') no-repeat;
  background-size: 100% 100%;
}

.par {
  font-family: PingFang SC-Regular, PingFang SC;
  color: #20263A;
}

/* 富文本--文字单独处理 */
.par .wxParse-b .WxEmojiView {
  line-height: 80rpx;
}

.par .wxParse-p .WxEmojiView {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 48rpx;
}