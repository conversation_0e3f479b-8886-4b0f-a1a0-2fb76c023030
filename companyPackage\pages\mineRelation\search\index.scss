@import '../../../../template/more/more.scss';
@import '../../../../template/null/null.scss';


/* input */
.pages {
    height: 100vh;
    overflow: hidden;
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

/* input */
.searchs {
    position: relative;
    z-index: 31;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 28rpx 24rpx;
    background: #eeeeee;
    border-radius: 8rpx;
    padding: 16rpx 0 16rpx 28rpx;
}

.s-input {
    display: flex;
    align-items: center;
    flex: 1;
}

.s-input-img {
    width: 40rpx;
    height: 40rpx;
}

input {
    caret-color: #E72410;
    color: #74798c;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
}


.s-input-item {
    display: flex;
    align-items: center;
    position: relative;
    flex: 1;
    height: 40rpx;
    padding-left: 16rpx;
    /* border-right: 1px solid hsla(229, 9%, 64%, 0.5); */
    /* border: 1px solid red; */
}

.s-input-item::after {
    content: "";
    height: 64rpx;
    width: 2px;
    background: #DEDEDE;
    /* background: red; */
    position: absolute;
    right: 0;
    /* top: 50%; */
    transform: scale(0.5);
}


.s-input-item-i {
    position: relative;
    flex: 1;
    height: 40rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #05070c;
}

.placeholder {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #9b9eac;
    line-height: 40rpx;
}

.search-cancel {
    height: 40rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: RIGHT;
    color: #20263a;
    padding: 0 28rpx;
}

.input-clear {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    /* border: 1px solid red; */
}

/* 清除x图标 */
.clearIcon {
    width: 32rpx;
    height: 32rpx;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA/UExURQAAAAAAAAAAAEVFRQAAANXV1QAAAEdwTBAQEPLy8kRERAAAAAAAAAAAAAAAAAAAANra2isrK7q6ur6+vv///20+0i0AAAAUdFJOU39oTpMT23QAg/OTIwQuCEbfi8nL3FdaYwAAANtJREFUOMuFk+sShSAIhMGorPFW+f7PeqhM0VPj/mLcb5pYAOYsZxVOABMq68orPAUpEFLUAMZDI28kQAh/QioATfCiiR7g3U8EAwbhQ2guwMOn/AnQXW+HLobet7sgBlL/IY6Z0GMMKY8ZXHoclkywv6zp2YGFlpA+2yXhRFQ+26LHi6h9tmVIJ1H70GTIRO230iHGoJvApc/fX0Qel421vw41gaLN9P81oUpQub+KsDlq0b8kXB7WIfpjYs/DyuMOov8hiHF3F6a/ct2l7a99/3D6p9c/3u/z/wGhjgzpIacRsQAAAABJRU5ErkJggg==') no-repeat;
    background-size: 100% 100%;
}


/* 缺省页 */
.queshen {
    width: 100%;
}

/* dialog文字样式 */
.dialog-con {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
}


/* 卡片 */
.bus-card .zhanwei {
    width: 100%;
    height: 0;
    border-top: 20rpx solid #eee;
}

.rcard {
    background: #fff;
    border-bottom: 20rpx solid #eee;
}

.rcard .head {
    height: 116rpx;
    padding: 28rpx 24rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.head-l {
    display: flex;
    align-items: center;
}

.rcard .head-l image {
    width: 60rpx;
    height: 60rpx;
    box-shadow: 2rpx 2rpx 2rpx 0rpx #f2f2f2;
    border-radius: 4rpx;
    overflow: hidden;
    margin-right: 24rpx;
}

.text-high {
  color: #E72410;
}

.rcard .head-l .title {
    width: 446rpx;
    height: 44rpx;
    font-size: 32rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263A;
    /*后期这里还要调整 */
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.head-r {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 108rpx;
    height: 48rpx;
    background: linear-gradient(90deg, #FFB2AA 0%,#E72410 100%);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
}

.head-r image {
    width: 20rpx;
    height: 20rpx;
    margin-right: 8rpx;
}

.head-r text {
    width: 48rpx;
    height: 34rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
}

.rcard .cont {
    height: 96rpx;
    line-height: 96rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* text-align: start; */
}
.rcard .cont > text {
  color: #1E75DB;
  text-align: start;
}

.rcard .cont > text .person_mun {
  width: auto;
  height: 36rpx;
  padding: 4rpx 16rpx;
  margin-left: 8rpx;
  background: rgba(30,117,219,0.1);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}


.rcard .cont > image {
  width: 20rpx;
  height: 20rpx;
}


/* pop相关 */
.pop-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pop-cancel, .pop-ok {
  width: 64rpx;
  height: 48rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 48rpx;
}
.pop-header >view:nth-child(1) {
  color: #74798C;
}

.pop-header >view:nth-child(3) {
  color:#E72410;
}

.pop-name {
    width: calc(100% - 128rpx);
    height: 60rpx;
    line-height: 60rpx;
    font-size: 32rpx;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    text-align: CENTER;
    color: #20263a;
}

.con .list {
    min-height: 220rpx;
    max-height: 400rpx;
    padding-bottom: 20rpx;
}

.con .list .list-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    padding: 28rpx 0;
    margin: 0 24rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #20263a;
}

.con .list .active {
  border-top: 1px solid #f7f7f7;
  border-bottom: 1px solid #f7f7f7;
  color: #E72410;
}

.con .btnbox {
    padding: 10rpx 0 58rpx;
    /* border-top: 1px solid #f7f7f7; */
    box-shadow: 8px 0px 8px 2px rgba(204.00000303983688, 204.00000303983688, 204.00000303983688, 0.20000000298023224);
    border-radius: 0px 0px 0px 0px;
    background: #FFFFFF;
}

.con .btn {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    width: 702rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: #eeeeee;
    border-radius: 8rpx;
    font-size: 34rpx;
    font-weight: 600;
    text-align: CENTER;
    color: #74798c;

}