@import '../../../template/more/more.scss';
@import '../../../template/null/null.scss';

.page_dailyDetail {
  border-top: 1px solid #eee;
}

.page_dailyDetail .select-box {
  height: 96rpx;
}

.page_dailyDetail .page_content {
  background-color: #f7f7f7;
  padding-top: 20rpx;
}

.item-box {
  background-color: #fff;
  margin-bottom: 20rpx;
}


.item-box .title-box {
  padding: 32rpx 24rpx;
  display: flex;
  border-bottom: 1px solid #eee;
}

.item-box .title-box .logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 2rpx;
  overflow: hidden;
  box-shadow: 0px 0px 6rpx #ddd;
  padding: 10rpx;
}

.item-box .title-box .logo image {
  width: 40rpx;
  height: 40rpx;
}

.item-box .title-box .name_box {
  flex: 1;
  margin: 0 24rpx;
}

.item-box .title-box .name_box .name {
  width: 100%;
  color: #20263a;
  font-size: 32rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 18rpx;
}

.title-box .name_box .tag-box {
  display: flex;
}

.name_box .tag-box .tags {
  height: 40rpx;
  line-height: 40rpx;
  padding: 0 12rpx;
  font-size: 24rpx;
  border-radius: 4rpx;
}

.name_box .tag-box .tags1 {
  color: #26C8A7;
  background-color: rgba(38, 200, 167, 0.1);
}

.name_box .tag-box .tags2 {
  color: #076ee4;
  background-color: rgba(7, 110, 228, 0.10);
}

.name_box .tag-box .tags3 {
  color: #FC7900;
  background-color: rgba(253, 121, 0, 0.1);
}

.name_box .tag-box .tags4 {
  color: #FFB93E;
  background-color: rgba(255, 185, 62, 0.1);
}

.name_box .tag-box .tags5 {
  color: #e72410;
  background-color: rgba(231, 36, 16, 0.10);
}

.name_box .tag-box .tags:not(:last-child) {
  margin-right: 16rpx;
}

.title-box .time {
  font-size: 24rpx;
  color: #9b9eac;
}

.dynamic_detail {
  padding: 28rpx 24rpx;
  font-size: 28rpx;
  color: #74798c;
}

.dynamic_detail .detail-items {
  display: flex;
  height: 40rpx;
  align-items: center;
}

.dynamic_detail .detail-items:not(:last-child) {
  margin-bottom: 20rpx;
}

.dynamic_detail .detail-items .label {
  width: 220rpx;
  color: #20263a;
}

.dynamic_detail .detail-items .text {
  text-align: right;
  width: calc(100% - 220rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}