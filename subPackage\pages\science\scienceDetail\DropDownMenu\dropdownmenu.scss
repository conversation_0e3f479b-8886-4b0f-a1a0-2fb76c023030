@import "../../../../../template/menuhead/index.scss";
@import "../../../../../components/hunt/index.scss";

.head {
  position: relative;
  z-index: 99;
}

.container {
  position: relative;
  z-index: 4;
  font-size: 14px;
}

.slidedown {
  transform: translateY(0%);
}

@keyframes slidown {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0%);
  }
}

.slidown {
  display: block;
  animation: slidown 0.2s ease-in both;
}

@keyframes slidup {
  from {
    transform: translateY(0%);
  }

  to {
    transform: translateY(-100%);
  }
}

.z-height {
  overflow-y: scroll;
  background: #fff;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.slidup {
  display: block;
  animation: slidup 0.2s ease-in both;
}

.disappear {
  display: none;
}

.show {
  display: block;
}

.container_hd {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow-y: scroll;
  background-color: rgba(0, 0, 0, 0.5);
}

.line-tesu {
  position: relative;
}

.line-tesu::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}


.nav-child.active .nav-title {
  color:  #E72410;
}

.nav-child.active .icon {
  border-bottom: 4px solid  #E72410;
  border-top: 0;
}


/* 筛选相关 */
/* companyPackage/pages/companyListFilter/companyListFilter.scss */

.container .footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  height: 85px;
  background-color: #fff;
  border-top: 2rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  padding: 10rpx 31rpx 0;
}

.container .footer text {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(90deg, #FFB2AA 0%, #E72410 100%);
  border-radius: 8rpx;
  color: #fff;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
}

.container .footer .reset {
  background: linear-gradient(74deg, #EEEEEE 0%, #F5F5F5 100%);
  font-size: 34rpx;
  text-align: CENTER;
  color: #74798c;
}

.active>input {
  color:  #E72410;
}