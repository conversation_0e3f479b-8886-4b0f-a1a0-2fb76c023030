@import "../../../../template/null/null.scss";

/* input */
.pages {
    height: 100vh;
    overflow: hidden;
    position: relative;
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

.page_top {
    width: 100%;
    position: absolute;
    top: 0;
}



/* 返回路由样式 */
.biaoqianf {
    scroll-behavior: smooth;
    overflow-x: scroll;
    width: 100vw;
    white-space: nowrap;
    background: #f7f7f7;
    position: relative;
    z-index: 210;
}

.biaoqian {
    display: inline-flex;
    align-items: center;
    height: 82rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #9B9EAC;
    width: auto;
}

.biaoqian .one {
    color: #3D4255;
}

.biaoqian .two {
    margin: 0 8rpx;
    width: 16rpx;
    height: 2rpx;
    background: #9B9EAC;
}

/*  */
.twonav {
    height: 96rpx;
    box-sizing: border-box;
    display: flex;
    padding: 0 24rpx;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    position: relative;
    z-index: 210;
}

.company_num {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 600;
    text-align: center;
    color: #20263a;
}

.company_num .color_num {
    color: #E72410;
}

.head_left {
    width: 216rpx;
    height: 96rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.left_box {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #74798c;
}

.left_box.active {
    color: #E72410;
}

.down_txt {
    padding-right: 12rpx;
}

.down_img {
    width: 20rpx;
    height: 20rpx;
}

.sort_list {
    height: 96rpx;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #74798c;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sort_list image {
    width: 32rpx;
    height: 32rpx;
}

.sort_list {
    border-top: 2rpx solid #eee;
}

.sort_list.active {
    color: #E72410;
}

/* 缺省页 */
.queshen {
    width: 100%;
}

/* 卡片  */
.card-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 100rpx;
    margin-top: 274rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
    background: #f7f7f7;
}

.card-box-text {
    width: 100%;
    height: 120rpx;
    line-height: 120rpx;
    text-align: center;
    background: #fff;
    border: 1px;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
    border-top: 1px dashed #eeeeee;
}

.his_content1_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
}

.his_content1_item-l {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #20263a;
}

.his_content1_item-r {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: RIGHT;
    color: #9b9eac;
}

/* dialog文字样式 */
.dialog-con {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
}

/* 筛选相关 */
.content .content-item-pop {
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 92rpx;
    align-items: center;
}

.content .content-item-pop::after {
    content: " ";
    width: 100%;
    height: 1px;
    background: #eee;
    position: absolute;
    bottom: 0;
    left: 0;
    transform: scaleY(0.5);
}

.content .content-item-pop-r {
    display: flex;
    align-items: center;
}

.content .content-item-pop-r text {
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263A;
}

.content .content-item-pop-r image {
    width: 24rpx;
    height: 24rpx;
    margin-left: 28rpx;
}

.content .content-item-pop .title {
    font-size: 28rpx;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #20263A;
}

.company-filter {
    /* height: 100%; */
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
}

.company-filter .head {
    display: flex;
    justify-content: space-between;
    padding: 25rpx 30rpx;
    background-color: #fff;
    align-items: center;
    font-size: 29rpx;
}

.company-filter .head .label-wrap {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #DEDEDE;
}

.company-filter .head .label {
    margin-right: 58rpx;
    color: #20263A;
}

.company-filter .head image {
    width: 27rpx;
    height: 27rpx;
}

.sort-list {
    z-index: 200;
    margin-top: 178rpx;
}

.sort-list-box {
    position: relative;
}

.sort-list-box .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 1;
    width: 100%;
    /* height: 85px; */
    background-color: #fff;
    border-top: 1px solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    padding: 25rpx 31rpx 0;
}

.sort-list-box .footer text {
    width: 340rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: linear-gradient(90deg, #FFB2AA 0%,#E72410 100%);
    border-radius: 8rpx;
    color: #fff;
    font-size: 34rpx;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
}

.sort-list-box .footer .reset {
    background: linear-gradient(74deg, #EEEEEE 0%, #F5F5F5 100%);
    font-size: 34rpx;
    text-align: CENTER;
    color: #74798c;
}

.company-filter .content {
    flex: 1;
    background-color: #fff;
    padding: 0 31rpx 34rpx;
    /* margin-top: 19rpx; */
    overflow-y: auto;
    border-top: 2rpx solid #f5f5f5;
}

.content .content-item {
    font-size: 25rpx;
    color: #5C6070;
    padding: 40rpx 0 20rpx;
    background-color: #fff;
}

.content-item .tit {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: 600;
    color: #20263a;
}

/* 筛选图标--多出 */
.filterexp {
    display: inline-block;
    margin-left: 12rpx;
    width: 32rpx;
    height: 32rpx;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAAXNSR0IArs4c6QAAANhQTFRFAAAAgICAn5+flZWqn5+vmZmzn5+1m5u2paW9n5+3oKi9nKO4o6O+n5+5o6O4o6i4oKW5oqe5oqa6n6O3oqW7oKO7oaW4oaW7oKa7oaS5oKW5oKW8oaS5oKW5oaO6oaa6oKS6oaW6oKS6oaW7oKW5oKS5oKa5oKS6oKW6oKW6oKS6oKW6oKS6oKW6oKS5oKW5oKS5oKW5oKS5oKS6oKW6oKW6oKS6oKW5oKW6oKS5oKS6oKW5oKW6oKS5oKW5oKW5oKS5oKS6oKW6oKW6oKS6oKW6oKW6oKW66g9uAAAAAEd0Uk5TAAQIDBAUGBwfICMkJygvLzM3P0BHS09PU1dbW19jZ2drb3N/i4+Pl5ebn5+jp6uvs7O3x8fLz8/P09PX19vf4+fr6/P39/vWY4YUAAABYklEQVQ4y32T2VrCMBBGxxIXrLivFVxBhSgoWhCLMWDsef838qaFtrbO1SwnmfmSf0QWpgI9nsM87AZK/tqm/mFh7n6rUPbaMRBHL1oPJgCu4+WOT4HX1mrSqzkGoo1lfceCOcyeODZgd9Now0K/Vuj5BHYz8afQ+Tt1ByIv9QZpdu/6ej/1+9AWEdlymMXEt5nLaobYF5F7OJYyQA5BiyjHeNn4YjS6WUZvOCUBtKTCWhBIF9aqgNWYnoSYTKp+cOBnwiljmTPMZG7zT/LCXEBXAxoEHv8H/m0xwMooN2QB+CAUDetVgIKunMNlFdCEM1GOjyrgHadEenBaDpzAg4j4MV+qDPAMriEi0obnlSS53WwulPiUwl5UKrk7mCRC8i08F0Rb64Otp9GuBXOSrR8ZsI3ML0fA+1UijLWLEJjUc2vQcQBmqB+HnwBx2yvM1Hhwy+X97vklAlNBN5zBLL/+v69tTgQXnGC4AAAAAElFTkSuQmCC");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.expPop {
    /* 企业规模说明弹窗 */
    font-size: 28rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263A;
    padding: 48rpx 32rpx 60rpx;
}


.content-item .wrap {
    display: flex;
    flex-wrap: wrap;

}

.content-item .wrap>text {
    padding: 0 19rpx;
    height: 52rpx;
    line-height: 52rpx;
    background-color: #F4F4F4;
    /* border: 2rpx solid #F4F4F4; */
    margin-right: 19rpx;
    margin-top: 25rpx;
    border-radius: 4rpx;
    font-weight: 400;
}

.content-item .wrap text.active {
    /* border: 2rpx solid #E72410; */
    color: #E72410;
    background: rgba(7, 110, 228, 0.16);
}

.content-item .wrap .input-wrap {
    display: flex;
    align-items: center;
    margin-top: 25rpx;
}

.content-item .wrap .input-wrap>view {
    display: flex;
    width: 350rpx;
    height: 56rpx;
    padding: 0 19rpx;
    background-color: #F4F4F4;
    border-radius: 4rpx;
    margin-right: 19rpx;
    color: #9B9EAC;

}

.content-item .wrap .input-wrap>view .year {
    flex-shrink: 0;
    line-height: 56rpx;
}

.content-item .wrap .input-wrap>view input {
    height: 100%;
}

.content-item .wrap .input-wrap>view .short-line {
    width: 200rpx;
    height: 100%;
    line-height: 52rpx;
    text-align: center;
}

.content-item .wrap .input-wrap>view.active {
    /* border: 2rpx solid #E72410; */
    color: #E72410 !important;
    background-color: rgba(7, 110, 228, 0.10);
}

.content-item .wrap .input-wrap view.active .plc {
    color: #E72410 !important;
}

/* 联系方式弹窗 */
.contact_box {
    width: 100%;
    height: 96rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.contact_box:not(:last-of-type)::after {
    content: " ";
    width: 100%;
    height: 2rpx;
    background: #EEEEEE;
    position: absolute;
    bottom: 0;
    transform: scaleY(0.5);
}

.contact_box:not(:last-of-type) {
    border-bottom: 2rpx solid #EEEEEE;
}

.contact_left {
    display: flex;
    align-items: end;
}

.contact_left image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
}

.contact_number {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #E72410;
    /* line-height: 28rpx; */
}

.contact_right {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
}

/* 写死标签 */
.threenav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;
    height: 96rpx;
    border-top: 1rpx solid #eee;
    background: #fff;
}

.threenav view {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    background: #FFFFFF;
    border-radius: 8rpx;
    border: 2rpx solid #EEEEEE;
    height: 58rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #74798C;
}

.threenav .end {
    border: 2rpx solid#E72410;
    color:#E72410;
    margin-left: 18rpx;
}