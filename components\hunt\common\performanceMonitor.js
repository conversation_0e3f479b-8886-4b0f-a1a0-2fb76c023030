/**
 * 性能监控工具
 * 监测组件性能，提供优化建议
 */

/**
 * 性能指标收集器
 */
class PerformanceMetrics {
  constructor() {
    this.metrics = {
      renderTime: [],
      updateTime: [],
      memoryUsage: [],
      setDataCalls: 0,
      componentUpdates: 0,
      searchOperations: 0
    };
    this.startTime = Date.now();
  }

  /**
   * 记录渲染时间
   * @param {number} duration - 渲染耗时
   */
  recordRenderTime(duration) {
    this.metrics.renderTime.push({
      duration,
      timestamp: Date.now()
    });
    
    // 保持最近100条记录
    if (this.metrics.renderTime.length > 100) {
      this.metrics.renderTime.shift();
    }
  }

  /**
   * 记录更新时间
   * @param {number} duration - 更新耗时
   */
  recordUpdateTime(duration) {
    this.metrics.updateTime.push({
      duration,
      timestamp: Date.now()
    });
    
    if (this.metrics.updateTime.length > 100) {
      this.metrics.updateTime.shift();
    }
  }

  /**
   * 记录内存使用情况
   */
  recordMemoryUsage() {
    // 在小程序环境中，我们模拟内存使用情况
    const usage = {
      timestamp: Date.now(),
      // 这里可以记录一些内存相关的指标
      objectCount: this.getObjectCount(),
      listenerCount: this.getListenerCount()
    };
    
    this.metrics.memoryUsage.push(usage);
    
    if (this.metrics.memoryUsage.length > 50) {
      this.metrics.memoryUsage.shift();
    }
  }

  /**
   * 增加setData调用计数
   */
  incrementSetDataCalls() {
    this.metrics.setDataCalls++;
  }

  /**
   * 增加组件更新计数
   */
  incrementComponentUpdates() {
    this.metrics.componentUpdates++;
  }

  /**
   * 增加搜索操作计数
   */
  incrementSearchOperations() {
    this.metrics.searchOperations++;
  }

  /**
   * 获取对象数量（模拟）
   */
  getObjectCount() {
    return Math.floor(Math.random() * 1000) + 500;
  }

  /**
   * 获取监听器数量（模拟）
   */
  getListenerCount() {
    return Math.floor(Math.random() * 50) + 10;
  }

  /**
   * 获取平均渲染时间
   */
  getAverageRenderTime() {
    if (this.metrics.renderTime.length === 0) return 0;
    
    const total = this.metrics.renderTime.reduce((sum, item) => sum + item.duration, 0);
    return total / this.metrics.renderTime.length;
  }

  /**
   * 获取平均更新时间
   */
  getAverageUpdateTime() {
    if (this.metrics.updateTime.length === 0) return 0;
    
    const total = this.metrics.updateTime.reduce((sum, item) => sum + item.duration, 0);
    return total / this.metrics.updateTime.length;
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const now = Date.now();
    const uptime = now - this.startTime;
    
    return {
      uptime,
      averageRenderTime: this.getAverageRenderTime(),
      averageUpdateTime: this.getAverageUpdateTime(),
      setDataCallsPerMinute: (this.metrics.setDataCalls / (uptime / 60000)).toFixed(2),
      componentUpdatesPerMinute: (this.metrics.componentUpdates / (uptime / 60000)).toFixed(2),
      searchOperationsPerMinute: (this.metrics.searchOperations / (uptime / 60000)).toFixed(2),
      memoryTrend: this.getMemoryTrend(),
      performanceScore: this.calculatePerformanceScore()
    };
  }

  /**
   * 获取内存趋势
   */
  getMemoryTrend() {
    if (this.metrics.memoryUsage.length < 2) return 'stable';
    
    const recent = this.metrics.memoryUsage.slice(-10);
    const first = recent[0];
    const last = recent[recent.length - 1];
    
    const objectTrend = last.objectCount - first.objectCount;
    const listenerTrend = last.listenerCount - first.listenerCount;
    
    if (objectTrend > 100 || listenerTrend > 10) return 'increasing';
    if (objectTrend < -100 || listenerTrend < -10) return 'decreasing';
    return 'stable';
  }

  /**
   * 计算性能评分
   */
  calculatePerformanceScore() {
    let score = 100;
    
    // 渲染时间评分
    const avgRenderTime = this.getAverageRenderTime();
    if (avgRenderTime > 100) score -= 20;
    else if (avgRenderTime > 50) score -= 10;
    
    // 更新时间评分
    const avgUpdateTime = this.getAverageUpdateTime();
    if (avgUpdateTime > 50) score -= 15;
    else if (avgUpdateTime > 25) score -= 8;
    
    // setData调用频率评分
    const uptime = Date.now() - this.startTime;
    const setDataRate = this.metrics.setDataCalls / (uptime / 60000);
    if (setDataRate > 60) score -= 25;
    else if (setDataRate > 30) score -= 15;
    
    // 内存趋势评分
    const memoryTrend = this.getMemoryTrend();
    if (memoryTrend === 'increasing') score -= 20;
    
    return Math.max(0, score);
  }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new PerformanceMetrics();
    this.isEnabled = true;
    this.reportInterval = null;
    this.thresholds = {
      renderTime: 100, // 毫秒
      updateTime: 50,  // 毫秒
      setDataRate: 30, // 每分钟
      memoryGrowth: 100 // 对象数量增长
    };
  }

  /**
   * 启用监控
   */
  enable() {
    this.isEnabled = true;
    this.startPeriodicReporting();
  }

  /**
   * 禁用监控
   */
  disable() {
    this.isEnabled = false;
    this.stopPeriodicReporting();
  }

  /**
   * 开始定期报告
   */
  startPeriodicReporting() {
    if (this.reportInterval) return;
    
    this.reportInterval = setInterval(() => {
      this.generateReport();
    }, 60000); // 每分钟生成一次报告
  }

  /**
   * 停止定期报告
   */
  stopPeriodicReporting() {
    if (this.reportInterval) {
      clearInterval(this.reportInterval);
      this.reportInterval = null;
    }
  }

  /**
   * 测量函数执行时间
   * @param {Function} fn - 要测量的函数
   * @param {string} type - 测量类型 ('render' | 'update')
   * @returns {*} 函数执行结果
   */
  measure(fn, type = 'render') {
    if (!this.isEnabled) return fn();
    
    const startTime = Date.now();
    const result = fn();
    const duration = Date.now() - startTime;
    
    if (type === 'render') {
      this.metrics.recordRenderTime(duration);
    } else if (type === 'update') {
      this.metrics.recordUpdateTime(duration);
    }
    
    // 检查是否超过阈值
    this.checkThresholds(type, duration);
    
    return result;
  }

  /**
   * 测量异步函数执行时间
   * @param {Function} fn - 要测量的异步函数
   * @param {string} type - 测量类型
   * @returns {Promise} 函数执行结果
   */
  async measureAsync(fn, type = 'render') {
    if (!this.isEnabled) return await fn();
    
    const startTime = Date.now();
    const result = await fn();
    const duration = Date.now() - startTime;
    
    if (type === 'render') {
      this.metrics.recordRenderTime(duration);
    } else if (type === 'update') {
      this.metrics.recordUpdateTime(duration);
    }
    
    this.checkThresholds(type, duration);
    
    return result;
  }

  /**
   * 记录setData调用
   */
  recordSetData() {
    if (!this.isEnabled) return;
    this.metrics.incrementSetDataCalls();
  }

  /**
   * 记录组件更新
   */
  recordComponentUpdate() {
    if (!this.isEnabled) return;
    this.metrics.incrementComponentUpdates();
  }

  /**
   * 记录搜索操作
   */
  recordSearchOperation() {
    if (!this.isEnabled) return;
    this.metrics.incrementSearchOperations();
  }

  /**
   * 检查性能阈值
   * @param {string} type - 类型
   * @param {number} value - 值
   */
  checkThresholds(type, value) {
    const threshold = this.thresholds[type];
    if (threshold && value > threshold) {
      console.warn(`Performance warning: ${type} took ${value}ms, threshold is ${threshold}ms`);
    }
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    if (!this.isEnabled) return null;
    
    const report = this.metrics.getPerformanceReport();
    
    // 记录内存使用情况
    this.metrics.recordMemoryUsage();
    
    // 输出报告到控制台（开发环境）
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      const systemInfo = wx.getSystemInfoSync();
      if (systemInfo.platform === 'devtools') {
        console.log('Performance Report:', report);
      }
    }
    
    return report;
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions() {
    const report = this.metrics.getPerformanceReport();
    const suggestions = [];
    
    if (report.averageRenderTime > 100) {
      suggestions.push('考虑使用虚拟滚动来减少渲染时间');
    }
    
    if (report.setDataCallsPerMinute > 30) {
      suggestions.push('减少setData调用频率，考虑批量更新');
    }
    
    if (report.memoryTrend === 'increasing') {
      suggestions.push('检查内存泄漏，及时清理不需要的对象和监听器');
    }
    
    if (report.performanceScore < 70) {
      suggestions.push('整体性能较低，建议进行全面优化');
    }
    
    return suggestions;
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.disable();
    this.metrics = null;
  }
}

module.exports = {
  PerformanceMonitor,
  PerformanceMetrics
};
