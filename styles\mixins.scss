// 全局 SCSS 混合 (Mixins)
// Global SCSS Mixins

// ==================== 布局混合 Layout Mixins ====================

// Flexbox 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox 垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// Flexbox 水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// Flexbox 两端对齐
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Flexbox 列布局
@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Flexbox 列布局居中
@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// ==================== 文本混合 Text Mixins ====================

// 文本省略号（单行）
@mixin text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 文本省略号（多行）
@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除文本选择
@mixin no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// ==================== 按钮混合 Button Mixins ====================

// 渐变按钮
@mixin gradient-button($gradient, $color: white) {
  background: $gradient;
  color: $color;
  border: none;
  border-radius: $border-radius-md;
  
  &:active {
    opacity: 0.8;
  }
}

// 圆形按钮
@mixin round-button($size: 80rpx) {
  width: $size;
  height: $size;
  border-radius: 50%;
  @include flex-center;
}

// 按钮禁用状态
@mixin button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

// ==================== 卡片混合 Card Mixins ====================

// 基础卡片
@mixin card-base {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-1;
  overflow: hidden;
}

// 卡片内边距
@mixin card-padding($padding: $spacing-lg) {
  padding: $padding;
}

// 卡片悬浮效果
@mixin card-hover {
  transition: box-shadow $duration-base $ease-out;
  
  &:hover {
    box-shadow: $shadow-2;
  }
}

// ==================== 输入框混合 Input Mixins ====================

// 基础输入框
@mixin input-base {
  width: 100%;
  padding: $spacing-md;
  border: $border-width solid $border-color;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  color: $text-color-primary;
  background: white;
  
  &::placeholder {
    color: $text-color-disabled;
  }
  
  &:focus {
    border-color: $primary-color;
    outline: none;
  }
  
  &:disabled {
    background: $gray-1;
    color: $text-color-disabled;
    cursor: not-allowed;
  }
}

// 输入框错误状态
@mixin input-error {
  border-color: $danger-color;
  
  &:focus {
    border-color: $danger-color;
  }
}

// ==================== 动画混合 Animation Mixins ====================

// 淡入动画
@mixin fade-in($duration: $duration-base) {
  animation: fadeIn $duration $ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 滑入动画
@mixin slide-in-up($duration: $duration-base) {
  animation: slideInUp $duration $ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 缩放动画
@mixin scale-in($duration: $duration-base) {
  animation: scaleIn $duration $ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// ==================== 响应式混合 Responsive Mixins ====================

// 媒体查询
@mixin media-sm {
  @media (min-width: $breakpoint-sm) {
    @content;
  }
}

@mixin media-md {
  @media (min-width: $breakpoint-md) {
    @content;
  }
}

@mixin media-lg {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

@mixin media-xl {
  @media (min-width: $breakpoint-xl) {
    @content;
  }
}

// ==================== 工具混合 Utility Mixins ====================

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 安全区域适配
@mixin safe-area-padding($position: bottom) {
  @if $position == bottom {
    padding-bottom: env(safe-area-inset-bottom);
  } @else if $position == top {
    padding-top: env(safe-area-inset-top);
  } @else if $position == left {
    padding-left: env(safe-area-inset-left);
  } @else if $position == right {
    padding-right: env(safe-area-inset-right);
  }
}

// 1px 边框解决方案
@mixin hairline($direction: all, $color: $border-color) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    
    @if $direction == all {
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      border: 1px solid $color;
      transform: scale(0.5);
      transform-origin: 0 0;
    } @else if $direction == top {
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: $color;
      transform: scaleY(0.5);
    } @else if $direction == bottom {
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: $color;
      transform: scaleY(0.5);
    } @else if $direction == left {
      top: 0;
      left: 0;
      width: 1px;
      height: 100%;
      background: $color;
      transform: scaleX(0.5);
    } @else if $direction == right {
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background: $color;
      transform: scaleX(0.5);
    }
  }
}
