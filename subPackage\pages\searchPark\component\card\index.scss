.fin-card {
  width: 100%;
  background: #ffffff;
  padding: 32rpx 24rpx 28rpx;
  border-top: 18rpx solid #f2f2f2;
  border-bottom: 2rpx solid #f2f2f2;
  display: flex;
}

.tag {
  padding: 2rpx 10rpx;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  background: rgba(253,147,49,0.1);
  color: #FD9331;
  line-height: 35rpx;
  margin-left: 8rpx;
}

.fin-card-l {
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
}

.fin-card-r {
  flex: 1;
}

.card_h {
  padding-left: 20rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  flex: 1;
  /* 12rpx */
  margin-bottom: 9rpx;
}

.card_h_l {
  /* display: flex; */
  width: 440rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  flex-wrap: nowrap;
  display: flex;
}

.card_h_l .text {
  display: inline;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.card_h_l .activetext {
  color: #E72410 !important;
}

.card_h_r {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 108rpx;
  height: 48rpx;
  /* opacity: 0.5; */
  border: 2rpx solid rgba(222, 222, 222, 0.5);
  border-radius: 4rpx;
}

.card_h_r text {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.card_h_r_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
}

.card_h_r_2 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 108rpx;
  height: 48rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  opacity: 1;
  border: 2rpx solid #E72410;
}

.card_h_r_2>view {
  width: 20rpx;
  height: 20rpx;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAAXNSR0IArs4c6QAAABtQTFRFAAAAAGvmCGvmBGzlBm7lBW/lBW3kBm7lB27k3u5XhQAAAAh0Uk5TAB8fO09jZ4fShlGuAAAAJ0lEQVQI12NgAAKPRgYYoBrTBQgq2kAkQwccIDOVgCCiGUQy0MgNADPzHDrqzoqoAAAAAElFTkSuQmCC");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.card_h_r_2>text {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #E72410;
  margin-left: 8rpx;
}

.card_tag {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.card_tag::after {
  content: "";
  width: 100%;
  height: 1px;
  background: #EEEEEE;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: scaleY(0.5);
}

.card_tag_box {
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  /* display: flex; */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  position: relative;
  padding-bottom: 20rpx;
}

.card_tag_i {
  display: inline-flex;
  padding: 2rpx 12rpx;
  min-width: 72rpx;
  margin-right: 16rpx;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  justify-content: center;
  align-items: center;
  border-radius: 4rpx;
}

.card_con {
  padding-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #EEEEEE;
}

.card_con_i {
  width: 33.3333%;
}

.card_con_i view:nth-of-type(1) {
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  padding-bottom: 8rpx;
}

.card_con_i view:nth-of-type(2) {
  text-align: center;
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  margin-bottom: 20rpx;
}

.industry {
  padding-top: 10rpx;
  width: 100%;
  height: 60rpx;
  overflow: hidden;       /* 隐藏溢出 */
  word-break: break-all;  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;  /* 超出部分省略号 */
  display: -webkit-box; /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical; /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 1; /** 显示的行数 **/
}

.industry-label {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
  line-height: 33rpx;
}

.industry-item {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
}