/**
 * Hunt 搜索组件
 * 简化的企业搜索组件，通过配置管理筛选项
 */

const {renderList} = require('./mixin.js');
const DataProcessor = require('./common/dataProcessor.js');
const BaseSearchBehavior = require('./common/baseSearchBehavior.js');

Component({
  behaviors: [BaseSearchBehavior],

  data: {
    itemList: JSON.parse(JSON.stringify(renderList)), // 页面静态数据
    leftList: JSON.parse(JSON.stringify(renderList)) // 页面静态数据-左边
  },

  methods: {
    /**
     * 获取组件类型
     * @returns {string} 组件类型
     */
    getComponentType() {
      return 'hunt';
    },

    /**
     * 获取过滤后的渲染列表
     * @returns {Array} 过滤后的列表
     */
    getFilteredRenderList() {
      return DataProcessor.filterDataByComponent(renderList, 'hunt');
    }
  },

  pageLifetimes: {
    show() {
      const {wrapHeight, isIphoneX} = this.data;

      // 计算容器高度
      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        });
      }

      this.setBackfillData(this.data.paramsData);
    }
  },

  observers: {
    vipVisible(val) {
      // 通过事件传递的方式告诉外面，需要vip弹窗
      console.log('是否需要弹vip弹窗', val);
      this.triggerEvent('vip', true);
    }
  }
});
