// Tailwind CSS 工具类 - 带 h 前缀
// 为了避免与现有样式冲突，所有类名都添加了 h- 前缀

// ==================== 布局 Layout ====================

// Display
.h-block {
  display: block;
}
.h-inline-block {
  display: inline-block;
}
.h-inline {
  display: inline;
}
.h-flex {
  display: flex;
}
.h-inline-flex {
  display: inline-flex;
}
.h-grid {
  display: grid;
}
.h-inline-grid {
  display: inline-grid;
}
.h-hidden {
  display: none;
}

// Position
.h-static {
  position: static;
}
.h-fixed {
  position: fixed;
}
.h-absolute {
  position: absolute;
}
.h-relative {
  position: relative;
}
.h-sticky {
  position: sticky;
}

// Top / Right / Bottom / Left
.h-inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.h-top-0 {
  top: 0;
}
.h-right-0 {
  right: 0;
}
.h-bottom-0 {
  bottom: 0;
}
.h-left-0 {
  left: 0;
}
.h-top-2 {
  top: 4rpx;
}
.h-right-2 {
  right: 4rpx;
}
.h-bottom-2 {
  bottom: 4rpx;
}
.h-left-2 {
  left: 4rpx;
}
.h-top-4 {
  top: 8rpx;
}
.h-right-4 {
  right: 8rpx;
}
.h-bottom-4 {
  bottom: 8rpx;
}
.h-left-4 {
  left: 8rpx;
}

// Z-index
.h-z-0 {
  z-index: 0;
}
.h-z-10 {
  z-index: 10;
}
.h-z-20 {
  z-index: 20;
}
.h-z-30 {
  z-index: 30;
}
.h-z-40 {
  z-index: 40;
}
.h-z-50 {
  z-index: 50;
}

// ==================== Flexbox ====================

// Flex Direction
.h-flex-row {
  flex-direction: row;
}
.h-flex-row-reverse {
  flex-direction: row-reverse;
}
.h-flex-col {
  flex-direction: column;
}
.h-flex-col-reverse {
  flex-direction: column-reverse;
}

// Flex Wrap
.h-flex-wrap {
  flex-wrap: wrap;
}
.h-flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.h-flex-nowrap {
  flex-wrap: nowrap;
}

// Flex
.h-flex-1 {
  flex: 1 1 0%;
}
.h-flex-auto {
  flex: 1 1 auto;
}
.h-flex-initial {
  flex: 0 1 auto;
}
.h-flex-none {
  flex: none;
}

// Justify Content
.h-justify-start {
  justify-content: flex-start;
}
.h-justify-end {
  justify-content: flex-end;
}
.h-justify-center {
  justify-content: center;
}
.h-justify-between {
  justify-content: space-between;
}
.h-justify-around {
  justify-content: space-around;
}
.h-justify-evenly {
  justify-content: space-evenly;
}

// Align Items
.h-items-start {
  align-items: flex-start;
}
.h-items-end {
  align-items: flex-end;
}
.h-items-center {
  align-items: center;
}
.h-items-baseline {
  align-items: baseline;
}
.h-items-stretch {
  align-items: stretch;
}

// Align Self
.h-self-auto {
  align-self: auto;
}
.h-self-start {
  align-self: flex-start;
}
.h-self-end {
  align-self: flex-end;
}
.h-self-center {
  align-self: center;
}
.h-self-stretch {
  align-self: stretch;
}

// ==================== 间距 Spacing ====================

// Padding
.h-p-0 {
  padding: 0;
}
.h-p-1 {
  padding: 2rpx;
}
.h-p-2 {
  padding: 4rpx;
}
.h-p-3 {
  padding: 6rpx;
}
.h-p-4 {
  padding: 8rpx;
}
.h-p-5 {
  padding: 10rpx;
}
.h-p-6 {
  padding: 12rpx;
}
.h-p-8 {
  padding: 16rpx;
}
.h-p-10 {
  padding: 20rpx;
}
.h-p-12 {
  padding: 24rpx;
}
.h-p-16 {
  padding: 32rpx;
}
.h-p-20 {
  padding: 40rpx;
}
.h-p-24 {
  padding: 48rpx;
}

// Padding X
.h-px-0 {
  padding-left: 0;
  padding-right: 0;
}
.h-px-1 {
  padding-left: 2rpx;
  padding-right: 2rpx;
}
.h-px-2 {
  padding-left: 4rpx;
  padding-right: 4rpx;
}
.h-px-3 {
  padding-left: 6rpx;
  padding-right: 6rpx;
}
.h-px-4 {
  padding-left: 8rpx;
  padding-right: 8rpx;
}
.h-px-5 {
  padding-left: 10rpx;
  padding-right: 10rpx;
}
.h-px-6 {
  padding-left: 12rpx;
  padding-right: 12rpx;
}
.h-px-8 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.h-px-10 {
  padding-left: 20rpx;
  padding-right: 20rpx;
}
.h-px-12 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}

// Padding Y
.h-py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.h-py-1 {
  padding-top: 2rpx;
  padding-bottom: 2rpx;
}
.h-py-2 {
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}
.h-py-3 {
  padding-top: 6rpx;
  padding-bottom: 6rpx;
}
.h-py-4 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}
.h-py-5 {
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}
.h-py-6 {
  padding-top: 12rpx;
  padding-bottom: 12rpx;
}
.h-py-8 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}
.h-py-10 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}
.h-py-12 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}

// Margin
.h-m-0 {
  margin: 0;
}
.h-m-1 {
  margin: 2rpx;
}
.h-m-2 {
  margin: 4rpx;
}
.h-m-3 {
  margin: 6rpx;
}
.h-m-4 {
  margin: 8rpx;
}
.h-m-5 {
  margin: 10rpx;
}
.h-m-6 {
  margin: 12rpx;
}
.h-m-8 {
  margin: 16rpx;
}
.h-m-10 {
  margin: 20rpx;
}
.h-m-12 {
  margin: 24rpx;
}
.h-m-16 {
  margin: 32rpx;
}
.h-m-20 {
  margin: 40rpx;
}
.h-m-24 {
  margin: 48rpx;
}
.h-m-auto {
  margin: auto;
}

// Margin X
.h-mx-0 {
  margin-left: 0;
  margin-right: 0;
}
.h-mx-1 {
  margin-left: 2rpx;
  margin-right: 2rpx;
}
.h-mx-2 {
  margin-left: 4rpx;
  margin-right: 4rpx;
}
.h-mx-3 {
  margin-left: 6rpx;
  margin-right: 6rpx;
}
.h-mx-4 {
  margin-left: 8rpx;
  margin-right: 8rpx;
}
.h-mx-5 {
  margin-left: 10rpx;
  margin-right: 10rpx;
}
.h-mx-6 {
  margin-left: 12rpx;
  margin-right: 12rpx;
}
.h-mx-8 {
  margin-left: 16rpx;
  margin-right: 16rpx;
}
.h-mx-10 {
  margin-left: 20rpx;
  margin-right: 20rpx;
}
.h-mx-12 {
  margin-left: 24rpx;
  margin-right: 24rpx;
}
.h-mx-auto {
  margin-left: auto;
  margin-right: auto;
}

// Margin Y
.h-my-0 {
  margin-top: 0;
  margin-bottom: 0;
}
.h-my-1 {
  margin-top: 2rpx;
  margin-bottom: 2rpx;
}
.h-my-2 {
  margin-top: 4rpx;
  margin-bottom: 4rpx;
}
.h-my-3 {
  margin-top: 6rpx;
  margin-bottom: 6rpx;
}
.h-my-4 {
  margin-top: 8rpx;
  margin-bottom: 8rpx;
}
.h-my-5 {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}
.h-my-6 {
  margin-top: 12rpx;
  margin-bottom: 12rpx;
}
.h-my-8 {
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}
.h-my-10 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.h-my-12 {
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}

// ==================== 尺寸 Sizing ====================

// Width
.h-w-0 {
  width: 0;
}
.h-w-1 {
  width: 2rpx;
}
.h-w-2 {
  width: 4rpx;
}
.h-w-3 {
  width: 6rpx;
}
.h-w-4 {
  width: 8rpx;
}
.h-w-5 {
  width: 10rpx;
}
.h-w-6 {
  width: 12rpx;
}
.h-w-8 {
  width: 16rpx;
}
.h-w-10 {
  width: 20rpx;
}
.h-w-12 {
  width: 24rpx;
}
.h-w-16 {
  width: 32rpx;
}
.h-w-20 {
  width: 40rpx;
}
.h-w-24 {
  width: 48rpx;
}
.h-w-32 {
  width: 64rpx;
}
.h-w-40 {
  width: 80rpx;
}
.h-w-48 {
  width: 96rpx;
}
.h-w-56 {
  width: 112rpx;
}
.h-w-64 {
  width: 128rpx;
}
.h-w-72 {
  width: 144rpx;
}
.h-w-80 {
  width: 160rpx;
}
.h-w-96 {
  width: 192rpx;
}
.h-w-auto {
  width: auto;
}
.h-w-full {
  width: 100%;
}
.h-w-screen {
  width: 100vw;
}

// Height
.h-h-0 {
  height: 0;
}
.h-h-1 {
  height: 2rpx;
}
.h-h-2 {
  height: 4rpx;
}
.h-h-3 {
  height: 6rpx;
}
.h-h-4 {
  height: 8rpx;
}
.h-h-5 {
  height: 10rpx;
}
.h-h-6 {
  height: 12rpx;
}
.h-h-8 {
  height: 16rpx;
}
.h-h-10 {
  height: 20rpx;
}
.h-h-12 {
  height: 24rpx;
}
.h-h-16 {
  height: 32rpx;
}
.h-h-20 {
  height: 40rpx;
}
.h-h-24 {
  height: 48rpx;
}
.h-h-32 {
  height: 64rpx;
}
.h-h-40 {
  height: 80rpx;
}
.h-h-48 {
  height: 96rpx;
}
.h-h-56 {
  height: 112rpx;
}
.h-h-64 {
  height: 128rpx;
}
.h-h-72 {
  height: 144rpx;
}
.h-h-80 {
  height: 160rpx;
}
.h-h-96 {
  height: 192rpx;
}
.h-h-auto {
  height: auto;
}
.h-h-full {
  height: 100%;
}
.h-h-screen {
  height: 100vh;
}

// ==================== 字体 Typography ====================

// Font Size
.h-text-xs {
  font-size: 20rpx;
  line-height: 24rpx;
}
.h-text-sm {
  font-size: 24rpx;
  line-height: 28rpx;
}
.h-text-base {
  font-size: 28rpx;
  line-height: 32rpx;
}
.h-text-lg {
  font-size: 32rpx;
  line-height: 36rpx;
}
.h-text-xl {
  font-size: 36rpx;
  line-height: 40rpx;
}
.h-text-2xl {
  font-size: 40rpx;
  line-height: 44rpx;
}
.h-text-3xl {
  font-size: 48rpx;
  line-height: 52rpx;
}
.h-text-4xl {
  font-size: 56rpx;
  line-height: 60rpx;
}

// Font Weight
.h-font-thin {
  font-weight: 100;
}
.h-font-light {
  font-weight: 300;
}
.h-font-normal {
  font-weight: 400;
}
.h-font-medium {
  font-weight: 500;
}
.h-font-semibold {
  font-weight: 600;
}
.h-font-bold {
  font-weight: 700;
}
.h-font-extrabold {
  font-weight: 800;
}
.h-font-black {
  font-weight: 900;
}

// Text Align
.h-text-left {
  text-align: left;
}
.h-text-center {
  text-align: center;
}
.h-text-right {
  text-align: right;
}
.h-text-justify {
  text-align: justify;
}

// Text Color
.h-text-white {
  color: #ffffff;
}
.h-text-black {
  color: #000000;
}
.h-text-gray-100 {
  color: #f7f8fa;
}
.h-text-gray-200 {
  color: #f2f3f5;
}
.h-text-gray-300 {
  color: #ebedf0;
}
.h-text-gray-400 {
  color: #dcdee0;
}
.h-text-gray-500 {
  color: #c8c9cc;
}
.h-text-gray-600 {
  color: #969799;
}
.h-text-gray-700 {
  color: #646566;
}
.h-text-gray-800 {
  color: #323233;
}
.h-text-red-500 {
  color: #e72410;
}
.h-text-blue-500 {
  color: #1989fa;
}
.h-text-green-500 {
  color: #07c160;
}
.h-text-yellow-500 {
  color: #ff976a;
}

// ==================== 背景 Background ====================

// Background Color
.h-bg-transparent {
  background-color: transparent;
}
.h-bg-white {
  background-color: #ffffff;
}
.h-bg-black {
  background-color: #000000;
}
.h-bg-gray-50 {
  background-color: #fafafa;
}
.h-bg-gray-100 {
  background-color: #f7f8fa;
}
.h-bg-gray-200 {
  background-color: #f2f3f5;
}
.h-bg-gray-300 {
  background-color: #ebedf0;
}
.h-bg-gray-400 {
  background-color: #dcdee0;
}
.h-bg-gray-500 {
  background-color: #c8c9cc;
}
.h-bg-red-500 {
  background-color: #e72410;
}
.h-bg-blue-500 {
  background-color: #1989fa;
}
.h-bg-green-500 {
  background-color: #07c160;
}
.h-bg-yellow-500 {
  background-color: #ff976a;
}

// ==================== 边框 Border ====================

// Border Width
.h-border-0 {
  border-width: 0;
}
.h-border {
  border-width: 1rpx;
}
.h-border-2 {
  border-width: 2rpx;
}
.h-border-4 {
  border-width: 4rpx;
}
.h-border-8 {
  border-width: 8rpx;
}

// Border Color
.h-border-transparent {
  border-color: transparent;
}
.h-border-white {
  border-color: #ffffff;
}
.h-border-gray-200 {
  border-color: #f2f3f5;
}
.h-border-gray-300 {
  border-color: #ebedf0;
}
.h-border-gray-400 {
  border-color: #dcdee0;
}
.h-border-red-500 {
  border-color: #e72410;
}
.h-border-blue-500 {
  border-color: #1989fa;
}

// Border Radius
.h-rounded-none {
  border-radius: 0;
}
.h-rounded-sm {
  border-radius: 4rpx;
}
.h-rounded {
  border-radius: 8rpx;
}
.h-rounded-md {
  border-radius: 12rpx;
}
.h-rounded-lg {
  border-radius: 16rpx;
}
.h-rounded-xl {
  border-radius: 20rpx;
}
.h-rounded-2xl {
  border-radius: 24rpx;
}
.h-rounded-full {
  border-radius: 9999rpx;
}

// ==================== 阴影 Shadow ====================

.h-shadow-none {
  box-shadow: none;
}
.h-shadow-sm {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.h-shadow {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.h-shadow-md {
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}
.h-shadow-lg {
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.1);
}

// ==================== 其他 Others ====================

// Overflow
.h-overflow-auto {
  overflow: auto;
}
.h-overflow-hidden {
  overflow: hidden;
}
.h-overflow-visible {
  overflow: visible;
}
.h-overflow-scroll {
  overflow: scroll;
}

// Opacity
.h-opacity-0 {
  opacity: 0;
}
.h-opacity-25 {
  opacity: 0.25;
}
.h-opacity-50 {
  opacity: 0.5;
}
.h-opacity-75 {
  opacity: 0.75;
}
.h-opacity-100 {
  opacity: 1;
}

// Cursor
.h-cursor-auto {
  cursor: auto;
}
.h-cursor-default {
  cursor: default;
}
.h-cursor-pointer {
  cursor: pointer;
}
.h-cursor-not-allowed {
  cursor: not-allowed;
}

// User Select
.h-select-none {
  user-select: none;
}
.h-select-text {
  user-select: text;
}
.h-select-all {
  user-select: all;
}
.h-select-auto {
  user-select: auto;
}

// ==================== 响应式工具类 Responsive ====================

// 小屏幕 (576px 以上)
@media (min-width: 576px) {
  .h-sm-block {
    display: block;
  }
  .h-sm-hidden {
    display: none;
  }
  .h-sm-flex {
    display: flex;
  }
  .h-sm-w-full {
    width: 100%;
  }
  .h-sm-w-1-2 {
    width: 50%;
  }
  .h-sm-text-sm {
    font-size: 24rpx;
  }
  .h-sm-text-base {
    font-size: 28rpx;
  }
  .h-sm-text-lg {
    font-size: 32rpx;
  }
}

// 中等屏幕 (768px 以上)
@media (min-width: 768px) {
  .h-md-block {
    display: block;
  }
  .h-md-hidden {
    display: none;
  }
  .h-md-flex {
    display: flex;
  }
  .h-md-w-full {
    width: 100%;
  }
  .h-md-w-1-2 {
    width: 50%;
  }
  .h-md-w-1-3 {
    width: 33.333333%;
  }
  .h-md-text-base {
    font-size: 28rpx;
  }
  .h-md-text-lg {
    font-size: 32rpx;
  }
  .h-md-text-xl {
    font-size: 36rpx;
  }
}

// 大屏幕 (992px 以上)
@media (min-width: 992px) {
  .h-lg-block {
    display: block;
  }
  .h-lg-hidden {
    display: none;
  }
  .h-lg-flex {
    display: flex;
  }
  .h-lg-w-full {
    width: 100%;
  }
  .h-lg-w-1-2 {
    width: 50%;
  }
  .h-lg-w-1-3 {
    width: 33.333333%;
  }
  .h-lg-w-1-4 {
    width: 25%;
  }
  .h-lg-text-lg {
    font-size: 32rpx;
  }
  .h-lg-text-xl {
    font-size: 36rpx;
  }
  .h-lg-text-2xl {
    font-size: 40rpx;
  }
}

// ==================== 动画 Animation ====================

// Transition
.h-transition-none {
  transition: none;
}
.h-transition-all {
  transition: all 0.15s ease-in-out;
}
.h-transition {
  transition:
    background-color,
    border-color,
    color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform 0.15s ease-in-out;
}
.h-transition-colors {
  transition:
    background-color,
    border-color,
    color,
    fill,
    stroke 0.15s ease-in-out;
}
.h-transition-opacity {
  transition: opacity 0.15s ease-in-out;
}
.h-transition-shadow {
  transition: box-shadow 0.15s ease-in-out;
}
.h-transition-transform {
  transition: transform 0.15s ease-in-out;
}

// Duration
.h-duration-75 {
  transition-duration: 75ms;
}
.h-duration-100 {
  transition-duration: 100ms;
}
.h-duration-150 {
  transition-duration: 150ms;
}
.h-duration-200 {
  transition-duration: 200ms;
}
.h-duration-300 {
  transition-duration: 300ms;
}
.h-duration-500 {
  transition-duration: 500ms;
}
.h-duration-700 {
  transition-duration: 700ms;
}
.h-duration-1000 {
  transition-duration: 1000ms;
}

// Ease
.h-ease-linear {
  transition-timing-function: linear;
}
.h-ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.h-ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.h-ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

// Transform
.h-transform {
  transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.h-transform-none {
  transform: none;
}

// Scale
.h-scale-0 {
  transform: scale(0);
}
.h-scale-50 {
  transform: scale(0.5);
}
.h-scale-75 {
  transform: scale(0.75);
}
.h-scale-90 {
  transform: scale(0.9);
}
.h-scale-95 {
  transform: scale(0.95);
}
.h-scale-100 {
  transform: scale(1);
}
.h-scale-105 {
  transform: scale(1.05);
}
.h-scale-110 {
  transform: scale(1.1);
}
.h-scale-125 {
  transform: scale(1.25);
}
.h-scale-150 {
  transform: scale(1.5);
}

// Rotate
.h-rotate-0 {
  transform: rotate(0deg);
}
.h-rotate-1 {
  transform: rotate(1deg);
}
.h-rotate-2 {
  transform: rotate(2deg);
}
.h-rotate-3 {
  transform: rotate(3deg);
}
.h-rotate-6 {
  transform: rotate(6deg);
}
.h-rotate-12 {
  transform: rotate(12deg);
}
.h-rotate-45 {
  transform: rotate(45deg);
}
.h-rotate-90 {
  transform: rotate(90deg);
}
.h-rotate-180 {
  transform: rotate(180deg);
}

// Translate
.h-translate-x-0 {
  transform: translateX(0);
}
.h-translate-x-1 {
  transform: translateX(2rpx);
}
.h-translate-x-2 {
  transform: translateX(4rpx);
}
.h-translate-x-4 {
  transform: translateX(8rpx);
}
.h-translate-x-8 {
  transform: translateX(16rpx);
}
.h-translate-x-16 {
  transform: translateX(32rpx);
}
.h-translate-x-full {
  transform: translateX(100%);
}
.h-translate-x-1-2 {
  transform: translateX(50%);
}

.h-translate-y-0 {
  transform: translateY(0);
}
.h-translate-y-1 {
  transform: translateY(2rpx);
}
.h-translate-y-2 {
  transform: translateY(4rpx);
}
.h-translate-y-4 {
  transform: translateY(8rpx);
}
.h-translate-y-8 {
  transform: translateY(16rpx);
}
.h-translate-y-16 {
  transform: translateY(32rpx);
}
.h-translate-y-full {
  transform: translateY(100%);
}
.h-translate-y-1-2 {
  transform: translateY(50%);
}

// ==================== 网格 Grid ====================

// Grid Template Columns
.h-grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.h-grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.h-grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.h-grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.h-grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.h-grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.h-grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

// Grid Template Rows
.h-grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr));
}
.h-grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}
.h-grid-rows-3 {
  grid-template-rows: repeat(3, minmax(0, 1fr));
}
.h-grid-rows-4 {
  grid-template-rows: repeat(4, minmax(0, 1fr));
}
.h-grid-rows-5 {
  grid-template-rows: repeat(5, minmax(0, 1fr));
}
.h-grid-rows-6 {
  grid-template-rows: repeat(6, minmax(0, 1fr));
}

// Gap
.h-gap-0 {
  gap: 0;
}
.h-gap-1 {
  gap: 2rpx;
}
.h-gap-2 {
  gap: 4rpx;
}
.h-gap-3 {
  gap: 6rpx;
}
.h-gap-4 {
  gap: 8rpx;
}
.h-gap-5 {
  gap: 10rpx;
}
.h-gap-6 {
  gap: 12rpx;
}
.h-gap-8 {
  gap: 16rpx;
}
.h-gap-10 {
  gap: 20rpx;
}
.h-gap-12 {
  gap: 24rpx;
}
.h-gap-16 {
  gap: 32rpx;
}

// Column Span
.h-col-auto {
  grid-column: auto;
}
.h-col-span-1 {
  grid-column: span 1 / span 1;
}
.h-col-span-2 {
  grid-column: span 2 / span 2;
}
.h-col-span-3 {
  grid-column: span 3 / span 3;
}
.h-col-span-4 {
  grid-column: span 4 / span 4;
}
.h-col-span-5 {
  grid-column: span 5 / span 5;
}
.h-col-span-6 {
  grid-column: span 6 / span 6;
}
.h-col-span-full {
  grid-column: 1 / -1;
}

// Row Span
.h-row-auto {
  grid-row: auto;
}
.h-row-span-1 {
  grid-row: span 1 / span 1;
}
.h-row-span-2 {
  grid-row: span 2 / span 2;
}
.h-row-span-3 {
  grid-row: span 3 / span 3;
}
.h-row-span-4 {
  grid-row: span 4 / span 4;
}
.h-row-span-5 {
  grid-row: span 5 / span 5;
}
.h-row-span-6 {
  grid-row: span 6 / span 6;
}
.h-row-span-full {
  grid-row: 1 / -1;
}
